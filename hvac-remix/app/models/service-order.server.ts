/**
 * Service Order Server Model - HVAC Service Management
 * Comprehensive service order management with workflow tracking
 */

import { prisma } from '~/db.server';
import type { ServiceOrder, ServiceOrderStatus, ServiceOrderPriority, ServiceOrderType, Prisma } from '@prisma/client';

export type { ServiceOrder, ServiceOrderStatus, ServiceOrderPriority, ServiceOrderType };

export interface ServiceOrderWithRelations extends ServiceOrder {
  customer?: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    address?: string;
    city?: string;
  };
  device?: {
    id: string;
    name: string;
    model?: string;
    type?: string;
    manufacturer?: string;
  };
  calendarEntry?: {
    id: string;
    startTime: Date;
    endTime: Date;
  };
  serviceReport?: {
    id: string;
    title: string;
    content?: string;
  };
}

export interface CreateServiceOrderData {
  title: string;
  description?: string;
  status?: ServiceOrderStatus;
  priority?: ServiceOrderPriority;
  type?: ServiceOrderType;
  scheduledDate?: Date;
  estimatedHours?: number;
  estimatedCost?: number;
  assignedTechnician?: string;
  partsRequired?: string;
  customerId: string;
  userId: string;
  deviceId?: string;
}

export interface UpdateServiceOrderData {
  title?: string;
  description?: string;
  status?: ServiceOrderStatus;
  priority?: ServiceOrderPriority;
  type?: ServiceOrderType;
  scheduledDate?: Date;
  completedDate?: Date;
  estimatedHours?: number;
  actualHours?: number;
  estimatedCost?: number;
  actualCost?: number;
  assignedTechnician?: string;
  technicianNotes?: string;
  partsRequired?: string;
  partsUsed?: string;
}

export interface ServiceOrderSearchFilters {
  search?: string;
  status?: ServiceOrderStatus;
  priority?: ServiceOrderPriority;
  type?: ServiceOrderType;
  customerId?: string;
  deviceId?: string;
  assignedTechnician?: string;
  scheduledDateFrom?: Date;
  scheduledDateTo?: Date;
}

/**
 * Get a service order by ID with full relations
 */
export async function getServiceOrder(id: string, userId: string): Promise<ServiceOrderWithRelations | null> {
  try {
    return await prisma.serviceOrder.findFirst({
      where: { 
        id,
        userId,
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            address: true,
            city: true,
          },
        },
        device: {
          select: {
            id: true,
            name: true,
            model: true,
            type: true,
            manufacturer: true,
          },
        },
        calendarEntry: {
          select: {
            id: true,
            startTime: true,
            endTime: true,
          },
        },
        serviceReport: {
          select: {
            id: true,
            title: true,
            content: true,
          },
        },
      },
    });
  } catch (error) {
    console.error('Error getting service order:', error);
    return null;
  }
}

/**
 * Get all service orders for a user with search and filtering
 */
export async function getServiceOrders(
  userId: string,
  filters: ServiceOrderSearchFilters = {},
  page: number = 1,
  limit: number = 20
): Promise<{ serviceOrders: ServiceOrderWithRelations[]; total: number }> {
  try {
    const where: Prisma.ServiceOrderWhereInput = {
      userId,
      ...(filters.search && {
        OR: [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
          { assignedTechnician: { contains: filters.search, mode: 'insensitive' } },
        ],
      }),
      ...(filters.status && { status: filters.status }),
      ...(filters.priority && { priority: filters.priority }),
      ...(filters.type && { type: filters.type }),
      ...(filters.customerId && { customerId: filters.customerId }),
      ...(filters.deviceId && { deviceId: filters.deviceId }),
      ...(filters.assignedTechnician && { 
        assignedTechnician: { contains: filters.assignedTechnician, mode: 'insensitive' } 
      }),
      ...(filters.scheduledDateFrom && filters.scheduledDateTo && {
        scheduledDate: {
          gte: filters.scheduledDateFrom,
          lte: filters.scheduledDateTo,
        },
      }),
    };

    const [serviceOrders, total] = await Promise.all([
      prisma.serviceOrder.findMany({
        where,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              address: true,
              city: true,
            },
          },
          device: {
            select: {
              id: true,
              name: true,
              model: true,
              type: true,
              manufacturer: true,
            },
          },
        },
        orderBy: [
          { priority: 'desc' }, // High priority first
          { scheduledDate: 'asc' }, // Earliest scheduled first
          { createdAt: 'desc' },
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.serviceOrder.count({ where }),
    ]);

    return { serviceOrders, total };
  } catch (error) {
    console.error('Error getting service orders:', error);
    return { serviceOrders: [], total: 0 };
  }
}

/**
 * Create a new service order
 */
export async function createServiceOrder(data: CreateServiceOrderData): Promise<ServiceOrder | null> {
  try {
    return await prisma.serviceOrder.create({
      data: {
        ...data,
        currency: 'PLN', // Default to Polish currency
      },
    });
  } catch (error) {
    console.error('Error creating service order:', error);
    return null;
  }
}

/**
 * Update a service order
 */
export async function updateServiceOrder(
  id: string, 
  userId: string, 
  data: UpdateServiceOrderData
): Promise<ServiceOrder | null> {
  try {
    return await prisma.serviceOrder.update({
      where: { 
        id,
        userId,
      },
      data,
    });
  } catch (error) {
    console.error('Error updating service order:', error);
    return null;
  }
}

/**
 * Delete a service order
 */
export async function deleteServiceOrder(id: string, userId: string): Promise<boolean> {
  try {
    await prisma.serviceOrder.delete({
      where: { 
        id,
        userId,
      },
    });
    return true;
  } catch (error) {
    console.error('Error deleting service order:', error);
    return false;
  }
}

/**
 * Get service order statistics for dashboard
 */
export async function getServiceOrderStats(userId: string) {
  try {
    const [total, byStatus, byPriority, scheduledToday, overdue] = await Promise.all([
      prisma.serviceOrder.count({ where: { userId } }),
      prisma.serviceOrder.groupBy({
        by: ['status'],
        where: { userId },
        _count: { status: true },
      }),
      prisma.serviceOrder.groupBy({
        by: ['priority'],
        where: { userId },
        _count: { priority: true },
      }),
      prisma.serviceOrder.count({
        where: {
          userId,
          scheduledDate: {
            gte: new Date(new Date().setHours(0, 0, 0, 0)),
            lt: new Date(new Date().setHours(23, 59, 59, 999)),
          },
        },
      }),
      prisma.serviceOrder.count({
        where: {
          userId,
          scheduledDate: {
            lt: new Date(),
          },
          status: {
            notIn: ['COMPLETED', 'CANCELLED'],
          },
        },
      }),
    ]);

    return {
      total,
      byStatus: byStatus.reduce((acc, item) => {
        acc[item.status] = item._count.status;
        return acc;
      }, {} as Record<string, number>),
      byPriority: byPriority.reduce((acc, item) => {
        acc[item.priority] = item._count.priority;
        return acc;
      }, {} as Record<string, number>),
      scheduledToday,
      overdue,
    };
  } catch (error) {
    console.error('Error getting service order stats:', error);
    return {
      total: 0,
      byStatus: {},
      byPriority: {},
      scheduledToday: 0,
      overdue: 0,
    };
  }
}

/**
 * Get upcoming service orders for technician dashboard
 */
export async function getUpcomingServiceOrders(
  userId: string,
  technician?: string,
  daysAhead: number = 7
): Promise<ServiceOrderWithRelations[]> {
  try {
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + daysAhead);

    return await prisma.serviceOrder.findMany({
      where: {
        userId,
        ...(technician && { assignedTechnician: technician }),
        scheduledDate: {
          gte: new Date(),
          lte: endDate,
        },
        status: {
          notIn: ['COMPLETED', 'CANCELLED'],
        },
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            address: true,
            city: true,
          },
        },
        device: {
          select: {
            id: true,
            name: true,
            model: true,
            type: true,
            manufacturer: true,
          },
        },
      },
      orderBy: { scheduledDate: 'asc' },
    });
  } catch (error) {
    console.error('Error getting upcoming service orders:', error);
    return [];
  }
}

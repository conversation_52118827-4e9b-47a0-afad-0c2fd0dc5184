/**
 * Opportunity Server Model - HVAC Sales Pipeline Management
 * 7-stage sales pipeline with HVAC-specific functionality
 */

import { prisma } from '~/db.server';
import type { Opportunity, OpportunityStage, HVACServiceType, InstallationComplexity, BuildingType, LeadSource, Prisma } from '@prisma/client';

export type { Opportunity, OpportunityStage, HVACServiceType, InstallationComplexity, BuildingType, LeadSource };

export interface OpportunityWithRelations extends Opportunity {
  customer?: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    address?: string;
    city?: string;
    priority?: string;
  };
}

export interface CreateOpportunityData {
  name: string;
  description?: string;
  stage?: OpportunityStage;
  probability?: number; // 0-100%
  value?: number;
  currency?: string;
  expectedCloseDate?: Date;
  serviceType?: HVACServiceType;
  equipmentType?: string;
  installationComplexity?: InstallationComplexity;
  roomCount?: number;
  totalArea?: number; // m²
  buildingType?: BuildingType;
  ownerId: string; // Sales rep/technician
  teamId?: string;
  leadSource?: LeadSource;
  referralSource?: string;
  customerId: string;
}

export interface UpdateOpportunityData {
  name?: string;
  description?: string;
  stage?: OpportunityStage;
  probability?: number;
  value?: number;
  currency?: string;
  expectedCloseDate?: Date;
  actualCloseDate?: Date;
  serviceType?: HVACServiceType;
  equipmentType?: string;
  installationComplexity?: InstallationComplexity;
  roomCount?: number;
  totalArea?: number;
  buildingType?: BuildingType;
  ownerId?: string;
  teamId?: string;
  leadSource?: LeadSource;
  referralSource?: string;
}

export interface OpportunitySearchFilters {
  search?: string;
  stage?: OpportunityStage;
  serviceType?: HVACServiceType;
  buildingType?: BuildingType;
  leadSource?: LeadSource;
  ownerId?: string;
  customerId?: string;
  valueMin?: number;
  valueMax?: number;
  expectedCloseDateFrom?: Date;
  expectedCloseDateTo?: Date;
}

/**
 * Get an opportunity by ID with full relations
 */
export async function getOpportunity(id: string, userId: string): Promise<OpportunityWithRelations | null> {
  try {
    return await prisma.opportunity.findFirst({
      where: { 
        id,
        customer: {
          userId, // Ensure user can only access their opportunities
        },
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            address: true,
            city: true,
            priority: true,
          },
        },
      },
    });
  } catch (error) {
    console.error('Error getting opportunity:', error);
    return null;
  }
}

/**
 * Get all opportunities for a user with search and filtering
 */
export async function getOpportunities(
  userId: string,
  filters: OpportunitySearchFilters = {},
  page: number = 1,
  limit: number = 20
): Promise<{ opportunities: OpportunityWithRelations[]; total: number }> {
  try {
    const where: Prisma.OpportunityWhereInput = {
      customer: {
        userId,
      },
      ...(filters.search && {
        OR: [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
          { equipmentType: { contains: filters.search, mode: 'insensitive' } },
        ],
      }),
      ...(filters.stage && { stage: filters.stage }),
      ...(filters.serviceType && { serviceType: filters.serviceType }),
      ...(filters.buildingType && { buildingType: filters.buildingType }),
      ...(filters.leadSource && { leadSource: filters.leadSource }),
      ...(filters.ownerId && { ownerId: filters.ownerId }),
      ...(filters.customerId && { customerId: filters.customerId }),
      ...(filters.valueMin && { value: { gte: filters.valueMin } }),
      ...(filters.valueMax && { value: { lte: filters.valueMax } }),
      ...(filters.expectedCloseDateFrom && filters.expectedCloseDateTo && {
        expectedCloseDate: {
          gte: filters.expectedCloseDateFrom,
          lte: filters.expectedCloseDateTo,
        },
      }),
    };

    const [opportunities, total] = await Promise.all([
      prisma.opportunity.findMany({
        where,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              address: true,
              city: true,
              priority: true,
            },
          },
        },
        orderBy: [
          { stage: 'asc' }, // Pipeline order
          { probability: 'desc' }, // High probability first
          { value: 'desc' }, // High value first
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.opportunity.count({ where }),
    ]);

    return { opportunities, total };
  } catch (error) {
    console.error('Error getting opportunities:', error);
    return { opportunities: [], total: 0 };
  }
}

/**
 * Get opportunities grouped by stage for pipeline view
 */
export async function getOpportunitiesByStage(userId: string): Promise<Record<OpportunityStage, OpportunityWithRelations[]>> {
  try {
    const opportunities = await prisma.opportunity.findMany({
      where: {
        customer: {
          userId,
        },
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            address: true,
            city: true,
            priority: true,
          },
        },
      },
      orderBy: [
        { probability: 'desc' },
        { value: 'desc' },
      ],
    });

    // Group by stage
    const grouped: Record<OpportunityStage, OpportunityWithRelations[]> = {
      NEW_LEAD: [],
      QUALIFIED: [],
      PROPOSAL: [],
      NEGOTIATION: [],
      IN_PROGRESS: [],
      CLOSED_WON: [],
      FOLLOW_UP: [],
    };

    opportunities.forEach(opportunity => {
      grouped[opportunity.stage].push(opportunity);
    });

    return grouped;
  } catch (error) {
    console.error('Error getting opportunities by stage:', error);
    return {
      NEW_LEAD: [],
      QUALIFIED: [],
      PROPOSAL: [],
      NEGOTIATION: [],
      IN_PROGRESS: [],
      CLOSED_WON: [],
      FOLLOW_UP: [],
    };
  }
}

/**
 * Create a new opportunity
 */
export async function createOpportunity(data: CreateOpportunityData): Promise<Opportunity | null> {
  try {
    return await prisma.opportunity.create({
      data: {
        ...data,
        currency: data.currency || 'PLN', // Default to Polish currency
      },
    });
  } catch (error) {
    console.error('Error creating opportunity:', error);
    return null;
  }
}

/**
 * Update an opportunity
 */
export async function updateOpportunity(
  id: string, 
  userId: string, 
  data: UpdateOpportunityData
): Promise<Opportunity | null> {
  try {
    return await prisma.opportunity.update({
      where: { 
        id,
        customer: {
          userId,
        },
      },
      data,
    });
  } catch (error) {
    console.error('Error updating opportunity:', error);
    return null;
  }
}

/**
 * Move opportunity to next stage
 */
export async function moveOpportunityToStage(
  id: string, 
  userId: string, 
  newStage: OpportunityStage,
  probability?: number
): Promise<Opportunity | null> {
  try {
    const updateData: UpdateOpportunityData = {
      stage: newStage,
      ...(probability !== undefined && { probability }),
    };

    // Set close date if moving to closed stages
    if (newStage === 'CLOSED_WON' || newStage === 'FOLLOW_UP') {
      updateData.actualCloseDate = new Date();
    }

    return await updateOpportunity(id, userId, updateData);
  } catch (error) {
    console.error('Error moving opportunity to stage:', error);
    return null;
  }
}

/**
 * Delete an opportunity
 */
export async function deleteOpportunity(id: string, userId: string): Promise<boolean> {
  try {
    await prisma.opportunity.delete({
      where: { 
        id,
        customer: {
          userId,
        },
      },
    });
    return true;
  } catch (error) {
    console.error('Error deleting opportunity:', error);
    return false;
  }
}

/**
 * Get opportunity statistics for dashboard
 */
export async function getOpportunityStats(userId: string) {
  try {
    const [total, byStage, totalValue, avgDealSize, conversionRate] = await Promise.all([
      prisma.opportunity.count({
        where: {
          customer: { userId },
        },
      }),
      prisma.opportunity.groupBy({
        by: ['stage'],
        where: {
          customer: { userId },
        },
        _count: { stage: true },
        _sum: { value: true },
      }),
      prisma.opportunity.aggregate({
        where: {
          customer: { userId },
        },
        _sum: { value: true },
      }),
      prisma.opportunity.aggregate({
        where: {
          customer: { userId },
        },
        _avg: { value: true },
      }),
      // Calculate conversion rate (CLOSED_WON / total)
      prisma.opportunity.count({
        where: {
          customer: { userId },
          stage: 'CLOSED_WON',
        },
      }),
    ]);

    return {
      total,
      byStage: byStage.reduce((acc, item) => {
        acc[item.stage] = {
          count: item._count.stage,
          value: item._sum.value || 0,
        };
        return acc;
      }, {} as Record<string, { count: number; value: number }>),
      totalValue: totalValue._sum.value || 0,
      avgDealSize: avgDealSize._avg.value || 0,
      conversionRate: total > 0 ? (conversionRate / total) * 100 : 0,
    };
  } catch (error) {
    console.error('Error getting opportunity stats:', error);
    return {
      total: 0,
      byStage: {},
      totalValue: 0,
      avgDealSize: 0,
      conversionRate: 0,
    };
  }
}

/**
 * Calendar Server Model - HVAC Calendar Management
 * 3-category calendar system with service integration
 */

import { prisma } from '~/db.server';
import type { CalendarEntry, CalendarCategory, Prisma } from '@prisma/client';

export type { CalendarEntry, CalendarCategory };

export interface CalendarEntryWithRelations extends CalendarEntry {
  serviceOrder?: {
    id: string;
    title: string;
    status: string;
    priority: string;
    customer: {
      id: string;
      name: string;
      phone?: string;
    };
    device?: {
      id: string;
      name: string;
      type?: string;
    };
  };
}

export interface CreateCalendarEntryData {
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  location?: string;
  isAllDay?: boolean;
  color?: string;
  category: CalendarCategory;
  customer?: string;
  technician?: string;
  device?: string;
  deviceCount?: number;
  priority?: string;
  status?: string;
  clientContact?: string;
  technicalIssues?: string;
  spareParts?: string;
  costAmount?: number;
  costCurrency?: string;
  costDescription?: string;
  userId: string;
  serviceOrderId?: string;
}

export interface UpdateCalendarEntryData {
  title?: string;
  description?: string;
  startTime?: Date;
  endTime?: Date;
  location?: string;
  isAllDay?: boolean;
  color?: string;
  category?: CalendarCategory;
  customer?: string;
  technician?: string;
  device?: string;
  deviceCount?: number;
  priority?: string;
  status?: string;
  clientContact?: string;
  technicalIssues?: string;
  spareParts?: string;
  costAmount?: number;
  costCurrency?: string;
  costDescription?: string;
}

export interface CalendarSearchFilters {
  category?: CalendarCategory;
  technician?: string;
  customer?: string;
  status?: string;
  priority?: string;
  startDate?: Date;
  endDate?: Date;
}

/**
 * Get a calendar entry by ID with full relations
 */
export async function getCalendarEntry(id: string, userId: string): Promise<CalendarEntryWithRelations | null> {
  try {
    return await prisma.calendarEntry.findFirst({
      where: { 
        id,
        userId,
      },
      include: {
        serviceOrder: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                phone: true,
              },
            },
            device: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
          },
        },
      },
    });
  } catch (error) {
    console.error('Error getting calendar entry:', error);
    return null;
  }
}

/**
 * Get calendar entries for a date range with filtering
 */
export async function getCalendarEntries(
  userId: string,
  startDate: Date,
  endDate: Date,
  filters: CalendarSearchFilters = {}
): Promise<CalendarEntryWithRelations[]> {
  try {
    const where: Prisma.CalendarEntryWhereInput = {
      userId,
      startTime: {
        gte: startDate,
        lte: endDate,
      },
      ...(filters.category && { category: filters.category }),
      ...(filters.technician && { 
        technician: { contains: filters.technician, mode: 'insensitive' } 
      }),
      ...(filters.customer && { 
        customer: { contains: filters.customer, mode: 'insensitive' } 
      }),
      ...(filters.status && { 
        status: { contains: filters.status, mode: 'insensitive' } 
      }),
      ...(filters.priority && { 
        priority: { contains: filters.priority, mode: 'insensitive' } 
      }),
    };

    return await prisma.calendarEntry.findMany({
      where,
      include: {
        serviceOrder: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                phone: true,
              },
            },
            device: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
          },
        },
      },
      orderBy: { startTime: 'asc' },
    });
  } catch (error) {
    console.error('Error getting calendar entries:', error);
    return [];
  }
}

/**
 * Get calendar entries for today
 */
export async function getTodayCalendarEntries(userId: string): Promise<CalendarEntryWithRelations[]> {
  const today = new Date();
  const startOfDay = new Date(today.setHours(0, 0, 0, 0));
  const endOfDay = new Date(today.setHours(23, 59, 59, 999));

  return getCalendarEntries(userId, startOfDay, endOfDay);
}

/**
 * Get upcoming calendar entries for technician dashboard
 */
export async function getUpcomingCalendarEntries(
  userId: string,
  technician?: string,
  daysAhead: number = 7
): Promise<CalendarEntryWithRelations[]> {
  const startDate = new Date();
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + daysAhead);

  const filters: CalendarSearchFilters = {};
  if (technician) {
    filters.technician = technician;
  }

  return getCalendarEntries(userId, startDate, endDate, filters);
}

/**
 * Create a new calendar entry
 */
export async function createCalendarEntry(data: CreateCalendarEntryData): Promise<CalendarEntry | null> {
  try {
    return await prisma.calendarEntry.create({
      data: {
        ...data,
        costCurrency: data.costCurrency || 'PLN',
      },
    });
  } catch (error) {
    console.error('Error creating calendar entry:', error);
    return null;
  }
}

/**
 * Update a calendar entry
 */
export async function updateCalendarEntry(
  id: string, 
  userId: string, 
  data: UpdateCalendarEntryData
): Promise<CalendarEntry | null> {
  try {
    return await prisma.calendarEntry.update({
      where: { 
        id,
        userId,
      },
      data,
    });
  } catch (error) {
    console.error('Error updating calendar entry:', error);
    return null;
  }
}

/**
 * Delete a calendar entry
 */
export async function deleteCalendarEntry(id: string, userId: string): Promise<boolean> {
  try {
    await prisma.calendarEntry.delete({
      where: { 
        id,
        userId,
      },
    });
    return true;
  } catch (error) {
    console.error('Error deleting calendar entry:', error);
    return false;
  }
}

/**
 * Get calendar statistics for dashboard
 */
export async function getCalendarStats(userId: string) {
  try {
    const today = new Date();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);

    const [todayEntries, weekEntries, byCategory, overdue] = await Promise.all([
      prisma.calendarEntry.count({
        where: {
          userId,
          startTime: {
            gte: new Date(today.setHours(0, 0, 0, 0)),
            lt: new Date(today.setHours(23, 59, 59, 999)),
          },
        },
      }),
      prisma.calendarEntry.count({
        where: {
          userId,
          startTime: {
            gte: startOfWeek,
            lte: endOfWeek,
          },
        },
      }),
      prisma.calendarEntry.groupBy({
        by: ['category'],
        where: { userId },
        _count: { category: true },
      }),
      prisma.calendarEntry.count({
        where: {
          userId,
          endTime: {
            lt: new Date(),
          },
          status: {
            not: 'COMPLETED',
          },
        },
      }),
    ]);

    return {
      todayEntries,
      weekEntries,
      byCategory: byCategory.reduce((acc, item) => {
        acc[item.category] = item._count.category;
        return acc;
      }, {} as Record<string, number>),
      overdue,
    };
  } catch (error) {
    console.error('Error getting calendar stats:', error);
    return {
      todayEntries: 0,
      weekEntries: 0,
      byCategory: {},
      overdue: 0,
    };
  }
}

/**
 * Get technician availability for scheduling
 */
export async function getTechnicianAvailability(
  userId: string,
  technician: string,
  date: Date
): Promise<{ available: boolean; conflictingEntries: CalendarEntryWithRelations[] }> {
  try {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const conflictingEntries = await prisma.calendarEntry.findMany({
      where: {
        userId,
        technician: {
          contains: technician,
          mode: 'insensitive',
        },
        startTime: {
          gte: startOfDay,
          lte: endOfDay,
        },
      },
      include: {
        serviceOrder: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                phone: true,
              },
            },
            device: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
          },
        },
      },
      orderBy: { startTime: 'asc' },
    });

    return {
      available: conflictingEntries.length === 0,
      conflictingEntries,
    };
  } catch (error) {
    console.error('Error checking technician availability:', error);
    return {
      available: false,
      conflictingEntries: [],
    };
  }
}

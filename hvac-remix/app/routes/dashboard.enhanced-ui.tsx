import type { MetaFunction, LoaderFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { EnhancedBentoDashboard } from "~/components/organisms/enhanced-bento-dashboard";
import { requireUserId } from "~/session.server";

export const meta: MetaFunction = () => [
  { title: "Enhanced UI Dashboard - HVAC CRM" },
  { name: "description", content: "Nowoczesny dashboard z Bento Grid layout i 2025 UI trends" }
];

interface DashboardData {
  todayAppointments: number;
  activeCustomers: number;
  pendingOrders: number;
  monthlyRevenue: number;
  equipmentAlerts: number;
  completedToday: number;
  avgResponseTime: string;
  weatherTemp: number;
  energyEfficiency: number;
  systemHealth: number;
}

export const loader: LoaderFunction = async ({ request }) => {
  const userId = await requireUserId(request);

  // Mock data for demonstration - in real app, fetch from database
  const dashboardData: DashboardData = {
    todayAppointments: 12,
    activeCustomers: 1247,
    pendingOrders: 8,
    monthlyRevenue: 125000,
    equipmentAlerts: 3,
    completedToday: 9,
    avgResponseTime: "1.2h",
    weatherTemp: 22,
    energyEfficiency: 87,
    systemHealth: 94
  };

  return json({ 
    dashboardData,
    user: { id: userId },
    timestamp: new Date().toISOString()
  });
};

export default function EnhancedUIDashboard() {
  const { dashboardData } = useLoaderData<typeof loader>();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Enhanced UI Dashboard
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Demonstracja nowoczesnego UI z Bento Grid layout i trendami 2025
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full text-sm font-medium">
                ✨ UI Trends 2025
              </div>
              <div className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm font-medium">
                🎨 Bento Layout
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Bento Dashboard */}
      <EnhancedBentoDashboard 
        data={dashboardData}
        userRole="technician"
      />

      {/* Features Showcase */}
      <div className="px-6 pb-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            🚀 Zaimplementowane Trendy UI/UX 2025
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-2">
              <h3 className="font-medium text-gray-900 dark:text-white flex items-center gap-2">
                📦 Bento Box Layout
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Modułowy układ inspirowany japońskimi pudełkami na lunch. Idealne dla dashboardów CRM.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="font-medium text-gray-900 dark:text-white flex items-center gap-2">
                ✨ Micro-Interactions
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Subtelne animacje i hover effects z Framer Motion dla lepszego UX.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="font-medium text-gray-900 dark:text-white flex items-center gap-2">
                🎨 Gradient Overlays
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Delikatne gradienty i efekty świetlne na hover dla nowoczesnego wyglądu.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="font-medium text-gray-900 dark:text-white flex items-center gap-2">
                📱 Responsive Design
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Adaptacyjny layout działający perfekcyjnie na wszystkich urządzeniach.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="font-medium text-gray-900 dark:text-white flex items-center gap-2">
                🌙 Dark Mode
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Przemyślany dark mode z odpowiednimi kontrastami i kolorami.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="font-medium text-gray-900 dark:text-white flex items-center gap-2">
                ⚡ Performance
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Optymalizowane komponenty z lazy loading i smooth animations.
              </p>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h3 className="font-medium text-blue-900 dark:text-blue-300 mb-2">
              🎯 Następne Kroki
            </h3>
            <ul className="text-sm text-blue-800 dark:text-blue-400 space-y-1">
              <li>• Implementacja AI-powered suggestions w formularzach</li>
              <li>• Dodanie immersive scrolling effects</li>
              <li>• Neumorphic design variants dla kart</li>
              <li>• Parallax effects na landing page</li>
              <li>• Advanced data visualizations</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSearchParams, Link } from "@remix-run/react";
import * as React from "react";
import { useState } from "react";
import {
  Search,
  Plus,
  Users,
  MapPin,
  Phone,
  Mail,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Calendar,
  DollarSign,
  Wrench,
  Building
} from "lucide-react";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { BentoGrid } from "~/components/atoms/bento-grid";
import { BentoCard } from "~/components/molecules/bento-card";
import { SmartSuggestions } from "~/components/molecules/smart-suggestions";
import {
  getCustomers,
  getCustomerStats,
  type CustomerWithRelations,
  type CustomerSearchFilters
} from "~/models/customer.server";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);

  // Parse search parameters
  const search = url.searchParams.get('search') || '';
  const priority = url.searchParams.get('priority') || '';
  const city = url.searchParams.get('city') || '';
  const page = parseInt(url.searchParams.get('page') || '1');
  const limit = parseInt(url.searchParams.get('limit') || '20');

  const filters: CustomerSearchFilters = {
    ...(search && { search }),
    ...(priority && { priority: priority as any }),
    ...(city && { city }),
  };

  try {
    const [customersData, stats] = await Promise.all([
      getCustomers(userId, filters, page, limit),
      getCustomerStats(userId),
    ]);

    // Enhanced dashboard data
    const dashboardData = {
      totalCustomers: customersData.total,
      newThisMonth: stats.recentlyAdded,
      urgentPriority: stats.byPriority.URGENT || 0,
      highPriority: stats.byPriority.HIGH || 0,
      avgResponseTime: "2.3h",
      satisfactionScore: 4.7,
      activeProjects: 23,
      recentActivity: [
        { id: 1, action: "Nowy klient: ABC Sp. z o.o.", time: "15 min temu", type: "new" },
        { id: 2, action: "Aktualizacja: XYZ Ltd.", time: "1h temu", type: "update" },
        { id: 3, action: "Zlecenie ukończone: DEF Corp", time: "2h temu", type: "completed" }
      ],
      topCities: [
        { name: "Warszawa", count: 45 },
        { name: "Kraków", count: 32 },
        { name: "Gdańsk", count: 28 }
      ]
    };

    return json({
      customers: customersData.customers,
      total: customersData.total,
      stats,
      filters,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(customersData.total / limit),
      },
      dashboardData,
    });
  } catch (error) {
    console.error('Error loading customers:', error);
    return json({
      customers: [],
      total: 0,
      stats: { total: 0, byPriority: {}, recentlyAdded: 0 },
      filters,
      pagination: { page: 1, limit: 20, totalPages: 0 },
      dashboardData: {
        totalCustomers: 0,
        newThisMonth: 0,
        urgentPriority: 0,
        highPriority: 0,
        avgResponseTime: "0h",
        satisfactionScore: 0,
        activeProjects: 0,
        recentActivity: [],
        topCities: []
      },
      error: 'Błąd podczas ładowania klientów',
    });
  }
}

// Polish translations
const translations = {
  customers: 'Klienci',
  search: 'Szukaj',
  addCustomer: 'Dodaj Klienta',
  name: 'Nazwa',
  email: 'Email',
  phone: 'Telefon',
  city: 'Miasto',
  priority: 'Priorytet',
  devices: 'Urządzenia',
  serviceOrders: 'Zlecenia Serwisowe',
  opportunities: 'Możliwości',
  actions: 'Akcje',
  edit: 'Edytuj',
  view: 'Zobacz',
  total: 'Łącznie',
  recentlyAdded: 'Dodano ostatnio',
  highPriority: 'Wysoki priorytet',
  mediumPriority: 'Średni priorytet',
  lowPriority: 'Niski priorytet',
  urgentPriority: 'Pilny',
  noCustomers: 'Brak klientów',
  loading: 'Ładowanie...',
};

const priorityColors = {
  URGENT: 'bg-red-100 text-red-800',
  HIGH: 'bg-orange-100 text-orange-800',
  MEDIUM: 'bg-yellow-100 text-yellow-800',
  LOW: 'bg-green-100 text-green-800',
};

const priorityLabels = {
  URGENT: translations.urgentPriority,
  HIGH: translations.highPriority,
  MEDIUM: translations.mediumPriority,
  LOW: translations.lowPriority,
};

export default function CustomersIndexPage() {
  const { customers, total, stats, filters, pagination, dashboardData, error } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(filters.search || "");
  const [showDashboard, setShowDashboard] = useState(true);

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const newParams = new URLSearchParams(searchParams);
    if (searchQuery) {
      newParams.set("search", searchQuery);
    } else {
      newParams.delete("search");
    }
    newParams.set("page", "1"); // Reset to first page on new search
    setSearchParams(newParams);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("page", newPage.toString());
    setSearchParams(newParams);
  };

  // Handle AI suggestion selection
  const handleSuggestionSelect = (suggestion: any) => {
    setSearchQuery(suggestion.text);
    const newParams = new URLSearchParams(searchParams);
    newParams.set("search", suggestion.text);
    newParams.set("page", "1");
    setSearchParams(newParams);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {translations.customers}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Zarządzaj bazą klientów i monitoruj relacje biznesowe
              </p>
            </div>
            <div className="flex gap-2">
              <Link to="/customers/map">
                <Button variant="outline" className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Mapa
                </Button>
              </Link>
              <Link to="/customers/new">
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  {translations.addCustomer}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        {/* Dashboard Overview */}
        {showDashboard && (
          <div className="mb-8">
            <BentoGrid>
              {/* Total Customers */}
              <BentoCard
                title="Wszyscy Klienci"
                value={dashboardData.totalCustomers}
                icon={<Users className="h-6 w-6" />}
                onClick={() => setShowDashboard(false)}
              />

              {/* New This Month */}
              <BentoCard
                title="Nowi w tym miesiącu"
                value={dashboardData.newThisMonth}
                trend={12.5}
                icon={<TrendingUp className="h-6 w-6" />}
              />

              {/* Urgent Priority */}
              <BentoCard
                title="Pilni Klienci"
                value={dashboardData.urgentPriority}
                icon={<AlertTriangle className="h-6 w-6" />}
              >
                <div className="mt-2">
                  <span className="text-xs text-red-600 dark:text-red-400 font-medium">
                    Wymaga natychmiastowej uwagi
                  </span>
                </div>
              </BentoCard>

              {/* Customer Satisfaction - Tall Card */}
              <BentoCard
                title="Zadowolenie Klientów"
                value={`${dashboardData.satisfactionScore}/5`}
                trend={5.2}
                icon={<CheckCircle className="h-6 w-6" />}
                size="tall"
              >
                <div className="mt-4 space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Średni czas odpowiedzi</span>
                    <span className="font-medium">{dashboardData.avgResponseTime}</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full w-[94%]" />
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Cel: 95%
                  </div>
                </div>
              </BentoCard>

              {/* Top Cities - Large Card */}
              <BentoCard
                title="Najważniejsze Miasta"
                icon={<Building className="h-6 w-6" />}
                size="large"
              >
                <div className="mt-4 space-y-3">
                  {dashboardData.topCities.map((city, index) => (
                    <div key={city.name} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${
                          index === 0 ? 'bg-yellow-500' :
                          index === 1 ? 'bg-gray-400' : 'bg-orange-500'
                        }`}>
                          {index + 1}
                        </div>
                        <span className="font-medium">{city.name}</span>
                      </div>
                      <span className="text-sm text-gray-600 dark:text-gray-400">{city.count} klientów</span>
                    </div>
                  ))}
                </div>
              </BentoCard>

              {/* Active Projects */}
              <BentoCard
                title="Aktywne Projekty"
                value={dashboardData.activeProjects}
                trend={8.3}
                icon={<Calendar className="h-6 w-6" />}
              />

              {/* High Priority */}
              <BentoCard
                title="Wysoki Priorytet"
                value={dashboardData.highPriority}
                icon={<DollarSign className="h-6 w-6" />}
              />

              {/* Recent Activity - Wide Card */}
              <BentoCard
                title="Ostatnia Aktywność"
                icon={<Wrench className="h-6 w-6" />}
                size="wide"
              >
                <div className="mt-4 space-y-2">
                  {dashboardData.recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium text-sm">{activity.action}</div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">{activity.time}</div>
                      </div>
                      <div className={`w-2 h-2 rounded-full ${
                        activity.type === 'completed' ? 'bg-green-500' :
                        activity.type === 'new' ? 'bg-blue-500' : 'bg-yellow-500'
                      }`} />
                    </div>
                  ))}
                </div>
              </BentoCard>
            </BentoGrid>
          </div>
        )}

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Wyszukiwanie Klientów
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Enhanced Search with AI */}
            <form onSubmit={handleSearch} className="mb-4">
              <div className="flex gap-2">
                <div className="flex-1 relative">
                  <Input
                    type="text"
                    placeholder="Szukaj klientów..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <SmartSuggestions
                    context={searchQuery}
                    type="customer_notes"
                    onSuggestionSelect={handleSuggestionSelect}
                  />
                </div>
                <Button type="submit" className="flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  {translations.search}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Error message */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {/* Customers list */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {customers.length > 0 ? (
            customers.map((customer: any) => (
              <CustomerCard key={customer.id} customer={customer} />
            ))
          ) : (
            <div className="col-span-full text-center py-8">
              <p className="text-gray-500">{translations.noCustomers}</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex justify-center mt-6">
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
              >
                Poprzednia
              </Button>

              <div className="flex items-center gap-1">
                {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant={page === pagination.page ? "default" : "outline"}
                    onClick={() => handlePageChange(page)}
                    className="w-10"
                  >
                    {page}
                  </Button>
                ))}
              </div>

              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.totalPages}
              >
                Następna
              </Button>
            </div>
          </div>
        )}

        {/* Summary */}
        <div className="mt-6 text-center text-gray-500">
          Wyświetlanie {customers.length} z {total} klientów
        </div>
      </div>
    </div>
  );
}

function CustomerCard({ customer }: { customer: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-start">
          <Link to={`/customers/${customer.id}`} className="hover:underline">
            {customer.name}
          </Link>
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${priorityColors[customer.priority]}`}>
            {priorityLabels[customer.priority]}
          </span>
        </CardTitle>
        <CardDescription>
          {customer.email || "Brak email"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {customer.phone && (
            <div>
              <Label>{translations.phone}</Label>
              <p>{customer.phone}</p>
            </div>
          )}
          {customer.address && (
            <div>
              <Label>Adres</Label>
              <p>{customer.address}</p>
              {customer.city && customer.postalCode && (
                <p>
                  {customer.city}, {customer.postalCode}
                  {customer.country ? `, ${customer.country}` : ""}
                </p>
              )}
            </div>
          )}
          {customer._count && (
            <div className="flex justify-between text-sm text-gray-500 pt-2">
              <span>🔧 {customer._count.devices}</span>
              <span>📋 {customer._count.serviceOrders}</span>
              <span>💼 {customer._count.opportunities}</span>
              <span>💰 {customer._count.invoices}</span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link to={`/customers/${customer.id}`}>
          <Button variant="outline">{translations.view}</Button>
        </Link>
        <Link to={`/customers/${customer.id}/edit`}>
          <Button variant="outline">{translations.edit}</Button>
        </Link>
      </CardFooter>
    </Card>
  );
}

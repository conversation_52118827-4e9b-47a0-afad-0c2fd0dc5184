/**
 * Equipment Management - Main List View
 * HVAC Equipment Registry Interface
 */

import { json, type LoaderFunctionArgs } from '@remix-run/node';
import { useLoaderData, useSearchParams, Link } from '@remix-run/react';
import { useState } from 'react';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Badge } from '~/components/ui/badge';
import { 
  getDevices, 
  getDeviceStats, 
  type DeviceWithRelations,
  type DeviceSearchFilters 
} from '~/models/device.server';
import { requireUserId } from '~/session.server';

// Polish translations
const translations = {
  equipment: 'Urządzenia',
  search: 'Szukaj',
  addEquipment: 'Dodaj <PERSON>',
  name: '<PERSON><PERSON><PERSON>',
  model: 'Model',
  manufacturer: 'Producent',
  type: 'Typ',
  serialNumber: 'Numer seryjny',
  customer: 'Klient',
  maintenanceDate: 'Data konserwacji',
  warrantyDate: 'Data gwarancji',
  capacity: 'Moc',
  efficiency: 'Efektywność',
  refrigerant: 'Czynnik',
  serviceOrders: 'Zlecenia',
  actions: 'Akcje',
  edit: 'Edytuj',
  view: 'Zobacz',
  total: 'Łącznie',
  maintenanceDue: 'Wymagana konserwacja',
  warrantyExpiring: 'Wygasająca gwarancja',
  noEquipment: 'Brak urządzeń',
  loading: 'Ładowanie...',
  installationDate: 'Data instalacji',
  status: 'Status',
  active: 'Aktywne',
  maintenanceRequired: 'Wymaga konserwacji',
  warrantyExpired: 'Gwarancja wygasła',
  all: 'Wszystkie',
};

const statusColors = {
  active: 'bg-green-100 text-green-800',
  maintenance: 'bg-yellow-100 text-yellow-800',
  warranty: 'bg-red-100 text-red-800',
};

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);
  
  // Parse search parameters
  const search = url.searchParams.get('search') || '';
  const manufacturer = url.searchParams.get('manufacturer') || '';
  const type = url.searchParams.get('type') || '';
  const customerId = url.searchParams.get('customerId') || '';
  const maintenanceDue = url.searchParams.get('maintenanceDue') === 'true';
  const warrantyExpiring = url.searchParams.get('warrantyExpiring') === 'true';
  const page = parseInt(url.searchParams.get('page') || '1');
  const limit = parseInt(url.searchParams.get('limit') || '20');

  const filters: DeviceSearchFilters = {
    ...(search && { search }),
    ...(manufacturer && { manufacturer }),
    ...(type && { type }),
    ...(customerId && { customerId }),
    ...(maintenanceDue && { maintenanceDue }),
    ...(warrantyExpiring && { warrantyExpiring }),
  };

  try {
    const [devicesData, stats] = await Promise.all([
      getDevices(userId, filters, page, limit),
      getDeviceStats(userId),
    ]);

    return json({
      devices: devicesData.devices,
      total: devicesData.total,
      stats,
      filters,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(devicesData.total / limit),
      },
    });
  } catch (error) {
    console.error('Error loading devices:', error);
    return json({
      devices: [],
      total: 0,
      stats: { total: 0, maintenanceDue: 0, warrantyExpiring: 0, byManufacturer: {} },
      filters,
      pagination: { page: 1, limit: 20, totalPages: 0 },
      error: 'Błąd podczas ładowania urządzeń',
    });
  }
}

export default function EquipmentIndex() {
  const { devices, total, stats, filters, pagination, error } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(filters.search || "");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const newParams = new URLSearchParams(searchParams);
    if (searchQuery) {
      newParams.set("search", searchQuery);
    } else {
      newParams.delete("search");
    }
    newParams.set("page", "1");
    setSearchParams(newParams);
  };

  const handleFilterChange = (key: string, value: string) => {
    const newParams = new URLSearchParams(searchParams);
    if (value) {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }
    newParams.set("page", "1");
    setSearchParams(newParams);
  };

  const getDeviceStatus = (device: DeviceWithRelations) => {
    const now = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    if (device.warrantyExpiryDate && device.warrantyExpiryDate <= now) {
      return { status: 'warranty', label: translations.warrantyExpired };
    }
    if (device.nextMaintenanceDate && device.nextMaintenanceDate <= thirtyDaysFromNow) {
      return { status: 'maintenance', label: translations.maintenanceRequired };
    }
    return { status: 'active', label: translations.active };
  };

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">{translations.equipment}</h1>
          <p className="text-gray-600">
            {translations.total}: {total} | {translations.maintenanceDue}: {stats.maintenanceDue}
          </p>
        </div>
        <Link to="/equipment/new">
          <Button>{translations.addEquipment}</Button>
        </Link>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium text-gray-500">{translations.total}</h3>
            <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium text-gray-500">{translations.maintenanceDue}</h3>
            <p className="text-2xl font-bold text-yellow-600">{stats.maintenanceDue}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium text-gray-500">{translations.warrantyExpiring}</h3>
            <p className="text-2xl font-bold text-red-600">{stats.warrantyExpiring}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium text-gray-500">Top Producent</h3>
            <p className="text-lg font-bold text-blue-600">
              {Object.keys(stats.byManufacturer)[0] || 'Brak'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search">{translations.search}</Label>
                <Input
                  type="text"
                  name="search"
                  id="search"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Nazwa, model, numer seryjny..."
                />
              </div>
              <div>
                <Label htmlFor="manufacturer">{translations.manufacturer}</Label>
                <Input
                  type="text"
                  name="manufacturer"
                  id="manufacturer"
                  defaultValue={filters.manufacturer || ''}
                  onChange={(e) => handleFilterChange('manufacturer', e.target.value)}
                  placeholder="Daikin, LG, Mitsubishi..."
                />
              </div>
              <div>
                <Label htmlFor="type">{translations.type}</Label>
                <Input
                  type="text"
                  name="type"
                  id="type"
                  defaultValue={filters.type || ''}
                  onChange={(e) => handleFilterChange('type', e.target.value)}
                  placeholder="Split AC, Heat Pump..."
                />
              </div>
              <div className="flex items-end">
                <Button type="submit" className="w-full">
                  {translations.search}
                </Button>
              </div>
            </div>
            
            {/* Quick Filters */}
            <div className="flex gap-2">
              <Button
                type="button"
                variant={filters.maintenanceDue ? "default" : "outline"}
                onClick={() => handleFilterChange('maintenanceDue', filters.maintenanceDue ? '' : 'true')}
              >
                {translations.maintenanceDue}
              </Button>
              <Button
                type="button"
                variant={filters.warrantyExpiring ? "default" : "outline"}
                onClick={() => handleFilterChange('warrantyExpiring', filters.warrantyExpiring ? '' : 'true')}
              >
                {translations.warrantyExpiring}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Equipment List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {devices.length > 0 ? (
          devices.map((device) => (
            <EquipmentCard key={device.id} device={device} getDeviceStatus={getDeviceStatus} />
          ))
        ) : (
          <div className="col-span-full text-center py-8">
            <p className="text-gray-500">{translations.noEquipment}</p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => {
                const newParams = new URLSearchParams(searchParams);
                newParams.set("page", String(pagination.page - 1));
                setSearchParams(newParams);
              }}
              disabled={pagination.page === 1}
            >
              Poprzednia
            </Button>
            <div className="flex items-center gap-1">
              {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={page === pagination.page ? "default" : "outline"}
                  onClick={() => {
                    const newParams = new URLSearchParams(searchParams);
                    newParams.set("page", String(page));
                    setSearchParams(newParams);
                  }}
                  className="w-10"
                >
                  {page}
                </Button>
              ))}
            </div>
            <Button
              variant="outline"
              onClick={() => {
                const newParams = new URLSearchParams(searchParams);
                newParams.set("page", String(pagination.page + 1));
                setSearchParams(newParams);
              }}
              disabled={pagination.page === pagination.totalPages}
            >
              Następna
            </Button>
          </div>
        </div>
      )}

      {/* Summary */}
      <div className="mt-6 text-center text-gray-500">
        Wyświetlanie {devices.length} z {total} urządzeń
      </div>
    </div>
  );
}

function EquipmentCard({ 
  device, 
  getDeviceStatus 
}: { 
  device: DeviceWithRelations; 
  getDeviceStatus: (device: DeviceWithRelations) => { status: string; label: string };
}) {
  const deviceStatus = getDeviceStatus(device);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-start">
          <Link to={`/equipment/${device.id}`} className="hover:underline">
            {device.name}
          </Link>
          <Badge className={statusColors[deviceStatus.status as keyof typeof statusColors]}>
            {deviceStatus.label}
          </Badge>
        </CardTitle>
        <CardDescription>
          {device.manufacturer} {device.model}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <Label>{translations.type}</Label>
              <p>{device.type || '-'}</p>
            </div>
            <div>
              <Label>{translations.capacity}</Label>
              <p>{device.capacity || '-'}</p>
            </div>
            <div>
              <Label>{translations.customer}</Label>
              <p>{device.customer?.name || '-'}</p>
            </div>
            <div>
              <Label>{translations.serviceOrders}</Label>
              <p>{device._count?.serviceOrders || 0}</p>
            </div>
          </div>
          
          {device.nextMaintenanceDate && (
            <div className="pt-2 border-t">
              <Label>{translations.maintenanceDate}</Label>
              <p className="text-sm">
                {new Date(device.nextMaintenanceDate).toLocaleDateString('pl-PL')}
              </p>
            </div>
          )}
        </div>
      </CardContent>
      <div className="p-4 pt-0 flex justify-between">
        <Link to={`/equipment/${device.id}`}>
          <Button variant="outline" size="sm">{translations.view}</Button>
        </Link>
        <Link to={`/equipment/${device.id}/edit`}>
          <Button variant="outline" size="sm">{translations.edit}</Button>
        </Link>
      </div>
    </Card>
  );
}

import type { ActionFunction } from "@remix-run/node";
import { json } from "@remix-run/node";

interface SuggestionRequest {
  context: string;
  type: 'hvac_service' | 'customer_notes' | 'equipment_issue';
  language: 'pl' | 'en';
}

interface AISuggestion {
  text: string;
  category: string;
  confidence: number;
  metadata?: Record<string, any>;
}

// HVAC-specific suggestion templates
const hvacSuggestions = {
  diagnostyka: [
    "Sprawdzenie poziomu czynnika chłodniczego",
    "Kontrola ciśnienia w systemie",
    "Pomiar temperatury na wlocie i wylocie",
    "Sprawdzenie działania termostatu",
    "Kontrola połączeń elektrycznych",
    "Sprawdzenie stanu izolacji przewodów",
    "Pomiar prądu pobieranego przez sprężarkę",
    "Kontrola szczelności systemu"
  ],
  konserwacja: [
    "Czyszczenie filtrów powietrza",
    "<PERSON><PERSON>szczenie parownika",
    "<PERSON><PERSON>szczenie skraplacza",
    "Kontrola i czyszczenie odpływu kondensatu",
    "Smarowanie łożysk wentylatora",
    "Sprawdzenie i dokręcenie połączeń",
    "Kontrola stanu pasków napędowych",
    "Czyszczenie obudowy jednostki"
  ],
  naprawa: [
    "Wymiana uszkodzonego wentylatora",
    "Naprawa nieszczelności w systemie",
    "Wymiana termostatu",
    "Naprawa połączeń elektrycznych",
    "Wymiana sprężarki",
    "Naprawa zaworu rozprężnego",
    "Wymiana czujników temperatury",
    "Naprawa sterownika"
  ],
  instalacja: [
    "Montaż jednostki wewnętrznej",
    "Montaż jednostki zewnętrznej",
    "Prowadzenie przewodów chłodniczych",
    "Podłączenie instalacji elektrycznej",
    "Wykonanie próby szczelności",
    "Napełnienie systemu czynnikiem",
    "Konfiguracja sterownika",
    "Uruchomienie i testy systemu"
  ]
};

function generateSuggestions(context: string, type: string): AISuggestion[] {
  const contextLower = context.toLowerCase();
  const suggestions: AISuggestion[] = [];

  // Keywords mapping to categories
  const keywords = {
    diagnostyka: ['problem', 'błąd', 'nie działa', 'sprawdzenie', 'kontrola', 'pomiar'],
    konserwacja: ['czyszczenie', 'serwis', 'konserwacja', 'przegląd', 'filtr'],
    naprawa: ['wymiana', 'naprawa', 'uszkodzenie', 'awaria', 'zepsuty'],
    instalacja: ['montaż', 'instalacja', 'podłączenie', 'uruchomienie']
  };

  // Score suggestions based on context
  Object.entries(keywords).forEach(([category, words]) => {
    const categoryScore = words.reduce((score, word) => {
      return score + (contextLower.includes(word) ? 1 : 0);
    }, 0);

    if (categoryScore > 0) {
      const categorySuggestions = hvacSuggestions[category as keyof typeof hvacSuggestions] || [];
      categorySuggestions.forEach(suggestion => {
        const confidence = Math.min(0.9, 0.5 + (categoryScore * 0.1) + Math.random() * 0.2);
        suggestions.push({
          text: suggestion,
          category: category.charAt(0).toUpperCase() + category.slice(1),
          confidence,
          metadata: { type, matchedWords: words.filter(w => contextLower.includes(w)) }
        });
      });
    }
  });

  // If no specific matches, provide general suggestions
  if (suggestions.length === 0) {
    const generalSuggestions = [
      { text: "Sprawdzenie ogólnego stanu urządzenia", category: "Diagnostyka", confidence: 0.7 },
      { text: "Kontrola podstawowych parametrów", category: "Diagnostyka", confidence: 0.65 },
      { text: "Czyszczenie podstawowych elementów", category: "Konserwacja", confidence: 0.6 }
    ];
    suggestions.push(...generalSuggestions);
  }

  // Sort by confidence and return top 5
  return suggestions
    .sort((a, b) => b.confidence - a.confidence)
    .slice(0, 5);
}

export const action: ActionFunction = async ({ request }) => {
  try {
    const body = await request.json() as SuggestionRequest;
    const { context, type, language } = body;

    if (!context || context.length < 3) {
      return json({ suggestions: [] });
    }

    // Generate AI-like suggestions based on context
    const suggestions = generateSuggestions(context, type);

    return json({ 
      suggestions,
      metadata: {
        type,
        language,
        contextLength: context.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('AI suggestions error:', error);
    return json(
      { error: 'Failed to generate suggestions' },
      { status: 500 }
    );
  }
};

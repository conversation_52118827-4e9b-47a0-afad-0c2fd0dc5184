import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Outlet, useLoaderData, useNavigate, Link } from "@remix-run/react";
import {
  Settings,
  Plus,
  Calendar,
  LayoutGrid,
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  <PERSON><PERSON>hart as LineChart<PERSON><PERSON>,
  Gauge as <PERSON>augeIcon,
  Save,
  Undo,
  Users,
  Wrench,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  MapPin,
  Thermometer,
  Zap
} from "lucide-react";
import { useState, useEffect } from "react";
import { DashboardHeader } from "~/components/molecules/dashboard-header";
import { QuickActions } from "~/components/molecules/quick-actions";
import { BentoGrid } from "~/components/atoms/bento-grid";
import { BentoCard } from "~/components/molecules/bento-card";
import { Avatar } from "~/components/ui/avatar";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { getUserSettings , convertToAppSettings } from "~/models/user-settings.server";
import { requireUser } from "~/session.server";
import type { UserRole } from "~/types/shared";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await requireUser(request);

  // Get user dashboard settings
  const dbSettings = await getUserSettings(user.id);
  const userSettings = dbSettings ? convertToAppSettings(dbSettings) : {
    theme: {
      theme: 'system',
      fontSize: 'medium',
      colorScheme: 'default'
    },
    dashboard: {
      showStats: true,
      showRecentOrders: true,
      showUpcomingEvents: true,
      showQuickActions: true,
      layout: 'bento',
      widgets: [
        { id: 'service-orders', position: 0, size: 'wide' },
        { id: 'device-types', position: 1, size: 'normal' },
        { id: 'revenue', position: 2, size: 'tall' },
        { id: 'technician-performance', position: 3, size: 'normal' },
        { id: 'maintenance-forecast', position: 4, size: 'large' }
      ],
      statsToShow: ['serviceOrders', 'customers', 'devices', 'revenue']
    },
    notifications: {
      email: true,
      inApp: true,
      push: false,
      serviceOrderUpdates: true,
      calendarReminders: true,
      systemUpdates: true
    }
  };

  // Mock dashboard data - in real app, fetch from database
  const dashboardData = {
    todayAppointments: 12,
    activeCustomers: 1247,
    pendingOrders: 8,
    monthlyRevenue: 125000,
    equipmentAlerts: 3,
    completedToday: 9,
    avgResponseTime: "1.2h",
    weatherTemp: 22,
    energyEfficiency: 87,
    systemHealth: 94,
    recentOrders: [
      { id: 1, customer: "Jan Kowalski", type: "Naprawa", status: "W trakcie", time: "10:30" },
      { id: 2, customer: "Anna Nowak", type: "Montaż", status: "Zaplanowane", time: "14:00" },
      { id: 3, customer: "Piotr Wiśniewski", type: "Przegląd", status: "Ukończone", time: "08:15" }
    ]
  };

  // Get available widgets based on user role
  const availableWidgets = getAvailableWidgetsForRole(user.role as UserRole);

  return json({
    user,
    settings: userSettings,
    availableWidgets,
    dashboardData
  });
};

// Helper function to get available widgets based on user role
function getAvailableWidgetsForRole(role: UserRole) {
  const baseWidgets = [
    { id: 'service-orders', name: 'Zlecenia serwisowe', icon: BarChart, description: 'Liczba zleceń serwisowych w czasie' },
    { id: 'device-types', name: 'Typy urządzeń', icon: PieChartIcon, description: 'Podział urządzeń według typu' },
    { id: 'upcoming-service', name: 'Nadchodzące zlecenia', icon: Calendar, description: 'Nadchodzące zlecenia serwisowe' },
  ];

  const managerWidgets = [
    { id: 'revenue', name: 'Przychody', icon: LineChartIcon, description: 'Przychody w czasie' },
    { id: 'technician-performance', name: 'Wydajność techników', icon: BarChart, description: 'Porównanie wydajności techników' },
    { id: 'customer-satisfaction', name: 'Zadowolenie klientów', icon: GaugeIcon, description: 'Wskaźnik zadowolenia klientów' },
  ];

  const adminWidgets = [
    { id: 'system-health', name: 'Stan systemu', icon: GaugeIcon, description: 'Monitorowanie stanu systemu' },
    { id: 'user-activity', name: 'Aktywność użytkowników', icon: LineChartIcon, description: 'Aktywność użytkowników w czasie' },
  ];

  const technicianWidgets = [
    { id: 'my-performance', name: 'Moja wydajność', icon: LineChartIcon, description: 'Twoje statystyki wydajności' },
    { id: 'my-schedule', name: 'Mój harmonogram', icon: Calendar, description: 'Twój harmonogram na najbliższy czas' },
  ];

  switch (role) {
    case 'ADMIN':
      return [...baseWidgets, ...managerWidgets, ...adminWidgets];
    case 'MANAGER':
      return [...baseWidgets, ...managerWidgets];
    case 'TECHNICIAN':
      return [...baseWidgets, ...technicianWidgets];
    default:
      return baseWidgets;
  }
}

export default function DashboardIndexPage() {
  const { user, settings, availableWidgets, dashboardData } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [isCustomizing, setIsCustomizing] = useState(false);
  const [currentLayout, setCurrentLayout] = useState(settings.dashboard.widgets || []);
  const userRole = user.role as UserRole;
  const userName = user?.name ? String(user.name) : undefined;
  const avatarUrl = undefined; // Placeholder for user avatar

  // Handle customization mode toggle
  const toggleCustomizationMode = () => {
    setIsCustomizing(!isCustomizing);
    if (isCustomizing) {
      // Save layout changes when exiting customization mode
      // This would typically be an API call
      console.log('Saving layout:', currentLayout);
    }
  };

  // Handle adding a new widget
  const handleAddWidget = (widgetId: string) => {
    const newWidget = {
      id: widgetId,
      position: currentLayout.length,
      size: 'normal'
    };
    setCurrentLayout([...currentLayout, newWidget]);
  };

  // Handle card clicks
  const handleCardClick = (cardType: string) => {
    switch (cardType) {
      case 'appointments':
        navigate('/calendar');
        break;
      case 'customers':
        navigate('/customers');
        break;
      case 'service-orders':
        navigate('/service-orders');
        break;
      case 'revenue':
        navigate('/reports/financial');
        break;
      default:
        console.log(`Clicked on ${cardType} card`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-6 py-4">
          <DashboardHeader
            userName={userName}
            avatarUrl={avatarUrl}
            isCustomizing={isCustomizing}
            onCustomizeToggle={toggleCustomizationMode}
          />
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        {/* Quick Actions */}
        {settings.dashboard.showQuickActions && !isCustomizing && (
          <div className="mb-8">
            <QuickActions userRole={userRole} className="animate-slide-up" />
          </div>
        )}

        {/* Customization Controls */}
        {isCustomizing && (
          <Card className="mb-8 border-primary/20 shadow-md animate-scale">
            <CardHeader className="bg-primary/5 rounded-t-lg border-b border-primary/10">
              <CardTitle className="text-gradient-primary">Dostosuj swój dashboard</CardTitle>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="flex flex-wrap gap-4 mb-6">
                {availableWidgets.map((widget, index) => (
                  <Button
                    key={widget.id}
                    variant="outline"
                    onClick={() => handleAddWidget(widget.id)}
                    className="flex items-center gap-2 hover-lift border-primary/20 animate-fade-in"
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <Plus className="h-4 w-4 text-primary" />
                    <widget.icon className="h-4 w-4 text-accent" />
                    <span>{widget.name}</span>
                  </Button>
                ))}
              </div>
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => setCurrentLayout(settings.dashboard.widgets)}
                  className="hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30"
                >
                  <Undo className="h-4 w-4 mr-2" />
                  Resetuj
                </Button>
                <Button
                  onClick={toggleCustomizationMode}
                  className="bg-primary hover:bg-primary-hover transition-colors"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Zapisz zmiany
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Enhanced Bento Dashboard */}
        <BentoGrid>
          {/* Today's Appointments - Wide Card */}
          <BentoCard
            title="Dzisiejsze Wizyty"
            value={dashboardData.todayAppointments}
            icon={<Calendar className="h-6 w-6" />}
            size="wide"
            onClick={() => handleCardClick('appointments')}
          >
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Zaplanowane</span>
                <span className="font-medium">{dashboardData.todayAppointments}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Ukończone</span>
                <span className="font-medium text-green-600">{dashboardData.completedToday}</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(dashboardData.completedToday / dashboardData.todayAppointments) * 100}%` }}
                />
              </div>
            </div>
          </BentoCard>

          {/* Active Customers */}
          <BentoCard
            title="Aktywni Klienci"
            value={dashboardData.activeCustomers.toLocaleString('pl-PL')}
            trend={5.2}
            icon={<Users className="h-6 w-6" />}
            onClick={() => handleCardClick('customers')}
          />

          {/* Equipment Alerts */}
          <BentoCard
            title="Alerty Sprzętu"
            value={dashboardData.equipmentAlerts}
            icon={<AlertTriangle className="h-6 w-6" />}
            onClick={() => handleCardClick('alerts')}
          >
            <div className="mt-2">
              <span className="text-xs text-orange-600 dark:text-orange-400 font-medium">
                Wymaga uwagi
              </span>
            </div>
          </BentoCard>

          {/* Monthly Revenue - Tall Card */}
          <BentoCard
            title="Przychód Miesięczny"
            value={`${dashboardData.monthlyRevenue.toLocaleString('pl-PL')} zł`}
            trend={12.5}
            icon={<DollarSign className="h-6 w-6" />}
            size="tall"
            onClick={() => handleCardClick('revenue')}
          >
            <div className="mt-4 space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Cel miesięczny</span>
                <span className="font-medium">85%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full w-[85%]" />
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Pozostało: 4 dni
              </div>
            </div>
          </BentoCard>

          {/* Recent Orders - Large Card */}
          <BentoCard
            title="Ostatnie Zlecenia"
            icon={<Wrench className="h-6 w-6" />}
            size="large"
            onClick={() => handleCardClick('service-orders')}
          >
            <div className="mt-4 space-y-3">
              {dashboardData.recentOrders.map((order) => (
                <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium text-sm">{order.customer}</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">{order.type}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-xs font-medium">{order.time}</div>
                    <div className={`text-xs px-2 py-1 rounded-full ${
                      order.status === 'Ukończone' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                      order.status === 'W trakcie' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                    }`}>
                      {order.status}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </BentoCard>

          {/* Weather Impact */}
          <BentoCard
            title="Wpływ Pogody"
            value={`${dashboardData.weatherTemp}°C`}
            icon={<Thermometer className="h-6 w-6" />}
            onClick={() => handleCardClick('weather')}
          >
            <div className="mt-2">
              <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                Optymalne warunki
              </span>
            </div>
          </BentoCard>

          {/* Response Time */}
          <BentoCard
            title="Czas Reakcji"
            value={dashboardData.avgResponseTime}
            icon={<Clock className="h-6 w-6" />}
            onClick={() => handleCardClick('response-time')}
          >
            <div className="mt-2">
              <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                Poniżej celu
              </span>
            </div>
          </BentoCard>

          {/* Energy Efficiency */}
          <BentoCard
            title="Efektywność Energetyczna"
            value={`${dashboardData.energyEfficiency}%`}
            trend={3.1}
            icon={<Zap className="h-6 w-6" />}
            onClick={() => handleCardClick('energy')}
          />

          {/* Quick Actions - Wide Card */}
          <BentoCard
            title="Szybkie Akcje"
            icon={<TrendingUp className="h-6 w-6" />}
            size="wide"
          >
            <div className="grid grid-cols-2 gap-3 mt-4">
              <button
                onClick={() => navigate('/service-orders/new')}
                className="flex items-center justify-center gap-2 p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors"
              >
                <Calendar className="h-4 w-4" />
                <span className="text-sm font-medium">Nowe Zlecenie</span>
              </button>
              <button
                onClick={() => navigate('/customers/new')}
                className="flex items-center justify-center gap-2 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors"
              >
                <Users className="h-4 w-4" />
                <span className="text-sm font-medium">Dodaj Klienta</span>
              </button>
              <button
                onClick={() => navigate('/technicians/route-planner')}
                className="flex items-center justify-center gap-2 p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors"
              >
                <MapPin className="h-4 w-4" />
                <span className="text-sm font-medium">Optymalizuj Trasę</span>
              </button>
              <button
                onClick={() => navigate('/reports')}
                className="flex items-center justify-center gap-2 p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg hover:bg-orange-200 dark:hover:bg-orange-900/50 transition-colors"
              >
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm font-medium">Raport Dnia</span>
              </button>
            </div>
          </BentoCard>
        </BentoGrid>

        {/* Outlet for nested routes */}
        <Outlet />
      </div>
    </div>
  );
}

import { j<PERSON>, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSearchParams, Link } from "@remix-run/react";
import * as React from "react";
import { useState } from "react";
import {
  Search,
  Plus,
  Filter,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  Users,
  Wrench,
  TrendingUp,
  BarChart3,
  MapPin
} from "lucide-react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { BentoGrid } from "~/components/atoms/bento-grid";
import { BentoCard } from "~/components/molecules/bento-card";
import { SmartSuggestions } from "~/components/molecules/smart-suggestions";
import {
  getServiceOrders,
  getServiceOrderStats,
  type ServiceOrderWithRelations,
  type ServiceOrderSearchFilters
} from "~/models/service-order.server";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);

  // Get pagination parameters from URL
  const page = parseInt(url.searchParams.get("page") || "1", 10);
  const pageSize = parseInt(url.searchParams.get("pageSize") || "10", 10);
  const orderBy = url.searchParams.get("orderBy") || "createdAt";
  const orderDirection = (url.searchParams.get("orderDirection") || "desc") as "asc" | "desc";

  // Get filter parameters from URL
  const search = url.searchParams.get("search") || undefined;
  const status = url.searchParams.get("status") || undefined;
  const priority = url.searchParams.get("priority") || undefined;
  const type = url.searchParams.get("type") || undefined;
  const customerId = url.searchParams.get("customerId") || undefined;
  const deviceId = url.searchParams.get("deviceId") || undefined;
  const dateFrom = url.searchParams.get("dateFrom") || undefined;
  const dateTo = url.searchParams.get("dateTo") || undefined;

  // Get service orders with pagination and filters
  const serviceOrdersResponse = await getServiceOrders(
    userId,
    { page, pageSize, orderBy, orderDirection },
    { search, status, priority, type, customerId, deviceId, dateFrom, dateTo }
  );

  // Get service order statistics for dashboard
  const stats = await getServiceOrderStats(userId);

  // Mock additional dashboard data
  const dashboardData = {
    totalOrders: serviceOrdersResponse.total || 0,
    pendingOrders: stats?.byStatus?.PENDING || 0,
    inProgressOrders: stats?.byStatus?.IN_PROGRESS || 0,
    completedToday: stats?.scheduledToday || 0,
    avgResponseTime: "1.2h",
    technicianUtilization: 87,
    customerSatisfaction: 4.8,
    urgentOrders: stats?.byPriority?.URGENT || 0,
    recentActivity: [
      { id: 1, action: "Zlecenie #1234 ukończone", time: "10 min temu", type: "completed" },
      { id: 2, action: "Nowe zlecenie od Jan Kowalski", time: "25 min temu", type: "new" },
      { id: 3, action: "Zlecenie #1230 w trakcie", time: "1h temu", type: "progress" }
    ]
  };

  return json({
    serviceOrders: serviceOrdersResponse.serviceOrders || [],
    totalCount: serviceOrdersResponse.total,
    totalPages: Math.ceil((serviceOrdersResponse.total || 0) / pageSize),
    currentPage: page,
    error: null,
    dashboardData,
    stats
  });
}

// Polish translations for Service Orders
const translations = {
  serviceOrders: 'Zlecenia Serwisowe',
  createServiceOrder: 'Utwórz Zlecenie Serwisowe',
  search: 'Szukaj',
  searchPlaceholder: 'Szukaj zleceń serwisowych...',
  status: 'Status',
  priority: 'Priorytet',
  type: 'Typ',
  all: 'Wszystkie',
  pending: 'Oczekujące',
  inProgress: 'W Trakcie',
  completed: 'Zakończone',
  cancelled: 'Anulowane',
  scheduled: 'Zaplanowane',
  waitingParts: 'Oczekiwanie na Części',
  qualityCheck: 'Kontrola Jakości',
  billed: 'Rozliczone',
  low: 'Niski',
  medium: 'Średni',
  high: 'Wysoki',
  urgent: 'Pilny',
  emergency: 'Awaryjny',
  maintenance: 'Konserwacja',
  repair: 'Naprawa',
  installation: 'Instalacja',
  inspection: 'Przegląd',
  warranty: 'Gwarancja',
  service: 'Serwis',
  noServiceOrders: 'Brak zleceń serwisowych',
  previous: 'Poprzednia',
  next: 'Następna',
  showing: 'Wyświetlanie',
  of: 'z',
  serviceOrdersText: 'zleceń serwisowych',
  viewDetails: 'Zobacz Szczegóły',
  edit: 'Edytuj',
  customer: 'Klient',
  device: 'Urządzenie',
  notScheduled: 'Nie zaplanowane',
};

const statusTranslations = {
  PENDING: translations.pending,
  SCHEDULED: translations.scheduled,
  IN_PROGRESS: translations.inProgress,
  WAITING_PARTS: translations.waitingParts,
  QUALITY_CHECK: translations.qualityCheck,
  COMPLETED: translations.completed,
  CANCELLED: translations.cancelled,
  BILLED: translations.billed,
};

const priorityTranslations = {
  LOW: translations.low,
  MEDIUM: translations.medium,
  HIGH: translations.high,
  URGENT: translations.urgent,
  EMERGENCY: translations.emergency,
};

const typeTranslations = {
  MAINTENANCE: translations.maintenance,
  REPAIR: translations.repair,
  INSTALLATION: translations.installation,
  INSPECTION: translations.inspection,
  EMERGENCY: translations.emergency,
  WARRANTY: translations.warranty,
  SERVICE: translations.service,
};

export default function ServiceOrdersIndexPage() {
  const { serviceOrders, totalCount, totalPages, currentPage, error, dashboardData, stats } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.get("search") || "");
  const [showDashboard, setShowDashboard] = useState(true);

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const newParams = new URLSearchParams(searchParams);
    if (searchQuery) {
      newParams.set("search", searchQuery);
    } else {
      newParams.delete("search");
    }
    newParams.set("page", "1"); // Reset to first page on new search
    setSearchParams(newParams);
  };

  // Handle AI suggestion selection
  const handleSuggestionSelect = (suggestion: any) => {
    setSearchQuery(suggestion.text);
    const newParams = new URLSearchParams(searchParams);
    newParams.set("search", suggestion.text);
    newParams.set("page", "1");
    setSearchParams(newParams);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("page", newPage.toString());
    setSearchParams(newParams);
  };

  // Handle status filter
  const handleStatusFilter = (status: string | null) => {
    const newParams = new URLSearchParams(searchParams);
    if (status) {
      newParams.set("status", status);
    } else {
      newParams.delete("status");
    }
    newParams.set("page", "1"); // Reset to first page on filter change
    setSearchParams(newParams);
  };

  // Handle priority filter
  const handlePriorityFilter = (priority: string | null) => {
    const newParams = new URLSearchParams(searchParams);
    if (priority) {
      newParams.set("priority", priority);
    } else {
      newParams.delete("priority");
    }
    newParams.set("page", "1"); // Reset to first page on filter change
    setSearchParams(newParams);
  };

  // Handle type filter
  const handleTypeFilter = (type: string | null) => {
    const newParams = new URLSearchParams(searchParams);
    if (type) {
      newParams.set("type", type);
    } else {
      newParams.delete("type");
    }
    newParams.set("page", "1"); // Reset to first page on filter change
    setSearchParams(newParams);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {translations.serviceOrders}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Zarządzaj zleceniami serwisowymi i monitoruj postępy
              </p>
            </div>
            <Link to="/service-orders/new">
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                {translations.createServiceOrder}
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        {/* Dashboard Overview */}
        {showDashboard && (
          <div className="mb-8">
            <BentoGrid>
              {/* Total Orders */}
              <BentoCard
                title="Wszystkie Zlecenia"
                value={dashboardData.totalOrders}
                icon={<Wrench className="h-6 w-6" />}
                onClick={() => setShowDashboard(false)}
              />

              {/* Pending Orders */}
              <BentoCard
                title="Oczekujące"
                value={dashboardData.pendingOrders}
                icon={<Clock className="h-6 w-6" />}
                onClick={() => handleStatusFilter('PENDING')}
              />

              {/* In Progress */}
              <BentoCard
                title="W Trakcie"
                value={dashboardData.inProgressOrders}
                icon={<AlertTriangle className="h-6 w-6" />}
                onClick={() => handleStatusFilter('IN_PROGRESS')}
              />

              {/* Completed Today */}
              <BentoCard
                title="Ukończone Dziś"
                value={dashboardData.completedToday}
                trend={15.2}
                icon={<CheckCircle className="h-6 w-6" />}
                onClick={() => handleStatusFilter('COMPLETED')}
              />

              {/* Technician Utilization - Tall Card */}
              <BentoCard
                title="Wykorzystanie Techników"
                value={`${dashboardData.technicianUtilization}%`}
                trend={3.1}
                icon={<Users className="h-6 w-6" />}
                size="tall"
              >
                <div className="mt-4 space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Średni czas reakcji</span>
                    <span className="font-medium">{dashboardData.avgResponseTime}</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full w-[87%]" />
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Cel: 90%
                  </div>
                </div>
              </BentoCard>

              {/* Recent Activity - Large Card */}
              <BentoCard
                title="Ostatnia Aktywność"
                icon={<TrendingUp className="h-6 w-6" />}
                size="large"
              >
                <div className="mt-4 space-y-3">
                  {dashboardData.recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium text-sm">{activity.action}</div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">{activity.time}</div>
                      </div>
                      <div className={`w-3 h-3 rounded-full ${
                        activity.type === 'completed' ? 'bg-green-500' :
                        activity.type === 'new' ? 'bg-blue-500' : 'bg-yellow-500'
                      }`} />
                    </div>
                  ))}
                </div>
              </BentoCard>

              {/* Customer Satisfaction */}
              <BentoCard
                title="Zadowolenie Klientów"
                value={`${dashboardData.customerSatisfaction}/5`}
                trend={2.3}
                icon={<BarChart3 className="h-6 w-6" />}
              />

              {/* Urgent Orders */}
              <BentoCard
                title="Pilne Zlecenia"
                value={dashboardData.urgentOrders}
                icon={<AlertTriangle className="h-6 w-6" />}
                onClick={() => handlePriorityFilter('URGENT')}
              >
                <div className="mt-2">
                  <span className="text-xs text-red-600 dark:text-red-400 font-medium">
                    Wymaga natychmiastowej uwagi
                  </span>
                </div>
              </BentoCard>
            </BentoGrid>
          </div>
        )}

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Wyszukiwanie i Filtry
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Enhanced Search with AI */}
            <form onSubmit={handleSearch} className="mb-6">
              <div className="flex gap-2">
                <div className="flex-1 relative">
                  <Input
                    type="text"
                    placeholder={translations.searchPlaceholder}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <SmartSuggestions
                    context={searchQuery}
                    type="hvac_service"
                    onSuggestionSelect={handleSuggestionSelect}
                  />
                </div>
                <Button type="submit" className="flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  {translations.search}
                </Button>
              </div>
            </form>

            {/* Status filters */}
            <div className="mb-4">
              <Label className="mb-2 block">{translations.status}</Label>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={!searchParams.get("status") ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleStatusFilter(null)}
                >
                  {translations.all}
                </Button>
                {["PENDING", "SCHEDULED", "IN_PROGRESS", "WAITING_PARTS", "QUALITY_CHECK", "COMPLETED", "CANCELLED", "BILLED"].map((status) => (
                  <Button
                    key={status}
                    variant={searchParams.get("status") === status ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleStatusFilter(status)}
                  >
                    {statusTranslations[status as keyof typeof statusTranslations] || status}
                  </Button>
                ))}
              </div>
            </div>

            {/* Priority filters */}
            <div className="mb-4">
              <Label className="mb-2 block">{translations.priority}</Label>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={!searchParams.get("priority") ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePriorityFilter(null)}
                >
                  {translations.all}
                </Button>
                {["LOW", "MEDIUM", "HIGH", "URGENT", "EMERGENCY"].map((priority) => (
                  <Button
                    key={priority}
                    variant={searchParams.get("priority") === priority ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePriorityFilter(priority)}
                  >
                    {priorityTranslations[priority as keyof typeof priorityTranslations] || priority}
                  </Button>
                ))}
              </div>
            </div>

            {/* Type filters */}
            <div className="mb-6">
              <Label className="mb-2 block">{translations.type}</Label>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={!searchParams.get("type") ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleTypeFilter(null)}
                >
                  {translations.all}
                </Button>
                {["MAINTENANCE", "REPAIR", "INSTALLATION", "INSPECTION", "EMERGENCY", "WARRANTY", "SERVICE"].map((type) => (
                  <Button
                    key={type}
                    variant={searchParams.get("type") === type ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleTypeFilter(type)}
                  >
                    {typeTranslations[type as keyof typeof typeTranslations] || type}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error message */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {/* Service Orders list */}
        <div className="space-y-4">
          {serviceOrders.length > 0 ? (
            serviceOrders.map((serviceOrder: any) => (
              <ServiceOrderCard key={serviceOrder.id} serviceOrder={serviceOrder} />
            ))
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">{translations.noServiceOrders}</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-6">
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                {translations.previous}
              </Button>

              <div className="flex items-center gap-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant={page === currentPage ? "default" : "outline"}
                    onClick={() => handlePageChange(page)}
                    className="w-10"
                  >
                    {page}
                  </Button>
                ))}
              </div>

              <Button
                variant="outline"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                {translations.next}
              </Button>
            </div>
          </div>
        )}

        {/* Summary */}
        <div className="mt-6 text-center text-gray-500">
          {translations.showing} {serviceOrders.length} {translations.of} {totalCount} {translations.serviceOrdersText}
        </div>
      </div>
    </div>
  );
}

function ServiceOrderCard({ serviceOrder }: { serviceOrder: any }) {
  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return translations.notScheduled;
    return new Date(dateString).toLocaleDateString('pl-PL');
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>
              <Link to={`/service-orders/${serviceOrder.id}`} className="hover:underline">
                {serviceOrder.title}
              </Link>
            </CardTitle>
            <CardDescription>
              {formatDate(serviceOrder.scheduledDate)}
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <span className={`px-2 py-1 rounded text-xs ${getStatusColor(serviceOrder.status)}`}>
              {statusTranslations[serviceOrder.status as keyof typeof statusTranslations] || serviceOrder.status}
            </span>
            <span className={`px-2 py-1 rounded text-xs ${getPriorityColor(serviceOrder.priority)}`}>
              {priorityTranslations[serviceOrder.priority as keyof typeof priorityTranslations] || serviceOrder.priority}
            </span>
            <span className={`px-2 py-1 rounded text-xs ${getTypeColor(serviceOrder.type)}`}>
              {typeTranslations[serviceOrder.type as keyof typeof typeTranslations] || serviceOrder.type}
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {serviceOrder.description && (
            <p className="text-sm text-gray-600 line-clamp-2">{serviceOrder.description}</p>
          )}

          <div className="flex flex-wrap gap-4 mt-2">
            {serviceOrder.customer && (
              <div>
                <Label className="text-xs">{translations.customer}</Label>
                <p className="text-sm">
                  <Link to={`/customers/${serviceOrder.customer.id}`} className="text-blue-500 hover:underline">
                    {serviceOrder.customer.name}
                  </Link>
                </p>
              </div>
            )}

            {serviceOrder.device && (
              <div>
                <Label className="text-xs">{translations.device}</Label>
                <p className="text-sm">
                  <Link to={`/devices/${serviceOrder.device.id}`} className="text-blue-500 hover:underline">
                    {serviceOrder.device.name}
                  </Link>
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link to={`/service-orders/${serviceOrder.id}`}>
          <Button variant="outline">{translations.viewDetails}</Button>
        </Link>
        <Link to={`/service-orders/${serviceOrder.id}/edit`}>
          <Button variant="outline">{translations.edit}</Button>
        </Link>
      </CardFooter>
    </Card>
  );
}

// Helper function to get status color
function getStatusColor(status: string) {
  switch (status.toUpperCase()) {
    case "PENDING":
      return "bg-yellow-100 text-yellow-800";
    case "IN_PROGRESS":
      return "bg-blue-100 text-blue-800";
    case "COMPLETED":
      return "bg-green-100 text-green-800";
    case "CANCELLED":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Helper function to get priority color
function getPriorityColor(priority: string) {
  switch (priority.toUpperCase()) {
    case "LOW":
      return "bg-green-100 text-green-800";
    case "MEDIUM":
      return "bg-blue-100 text-blue-800";
    case "HIGH":
      return "bg-orange-100 text-orange-800";
    case "URGENT":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Helper function to get type color
function getTypeColor(type: string) {
  switch (type?.toUpperCase()) {
    case "SERVICE":
      return "bg-blue-100 text-blue-800";
    case "INSTALLATION":
      return "bg-green-100 text-green-800";
    case "INSPECTION":
      return "bg-yellow-100 text-yellow-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

/**
 * Sales Pipeline - Kanban Board View
 * 7-Stage HVAC Sales Pipeline Management
 */

import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from '@remix-run/node';
import { useLoaderData, useFetcher, <PERSON> } from '@remix-run/react';
import { useState } from 'react';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { 
  getOpportunitiesByStage, 
  getOpportunityStats,
  moveOpportunityToStage,
  type OpportunityWithRelations,
  type OpportunityStage 
} from '~/models/opportunity.server';
import { requireUserId } from '~/session.server';

// Polish translations
const translations = {
  salesPipeline: 'Lejek <PERSON>',
  addOpportunity: 'Doda<PERSON>',
  totalValue: '<PERSON>ączna wartość',
  avgDealSize: 'Średnia wartość',
  conversionRate: 'Wskaźnik konwersji',
  // Stage translations
  NEW_LEAD: 'Nowy Lead',
  QUALIFIED: 'Zakwalifikowany',
  PROPOSAL: 'Propozycja',
  NEGOTIATION: 'Negocjacje',
  IN_PROGRESS: 'W realizacji',
  CLOSED_WON: 'Zamknięte - Wygrane',
  FOLLOW_UP: 'Follow-up',
  // Service types
  MAINTENANCE: 'Konserwacja',
  INSTALLATION: 'Instalacja',
  REPAIR: 'Naprawa',
  CONSULTATION: 'Konsultacja',
  EMERGENCY: 'Awaria',
  UPGRADE: 'Modernizacja',
  // Building types
  RESIDENTIAL: 'Mieszkaniowy',
  COMMERCIAL: 'Komercyjny',
  INDUSTRIAL: 'Przemysłowy',
  INSTITUTIONAL: 'Instytucjonalny',
  // Actions
  view: 'Zobacz',
  edit: 'Edytuj',
  moveToStage: 'Przenieś do etapu',
  customer: 'Klient',
  value: 'Wartość',
  probability: 'Prawdopodobieństwo',
  expectedClose: 'Oczekiwane zamknięcie',
  serviceType: 'Typ usługi',
  buildingType: 'Typ budynku',
};

const stageColors = {
  NEW_LEAD: 'bg-gray-100 border-gray-300',
  QUALIFIED: 'bg-blue-100 border-blue-300',
  PROPOSAL: 'bg-yellow-100 border-yellow-300',
  NEGOTIATION: 'bg-orange-100 border-orange-300',
  IN_PROGRESS: 'bg-purple-100 border-purple-300',
  CLOSED_WON: 'bg-green-100 border-green-300',
  FOLLOW_UP: 'bg-emerald-100 border-emerald-300',
};

const stageOrder: OpportunityStage[] = [
  'NEW_LEAD',
  'QUALIFIED', 
  'PROPOSAL',
  'NEGOTIATION',
  'IN_PROGRESS',
  'CLOSED_WON',
  'FOLLOW_UP'
];

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);

  try {
    const [opportunitiesByStage, stats] = await Promise.all([
      getOpportunitiesByStage(userId),
      getOpportunityStats(userId),
    ]);

    return json({
      opportunitiesByStage,
      stats,
    });
  } catch (error) {
    console.error('Error loading sales pipeline:', error);
    return json({
      opportunitiesByStage: {
        NEW_LEAD: [],
        QUALIFIED: [],
        PROPOSAL: [],
        NEGOTIATION: [],
        IN_PROGRESS: [],
        CLOSED_WON: [],
        FOLLOW_UP: [],
      },
      stats: {
        total: 0,
        byStage: {},
        totalValue: 0,
        avgDealSize: 0,
        conversionRate: 0,
      },
      error: 'Błąd podczas ładowania lejka sprzedaży',
    });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const formData = await request.formData();
  const action = formData.get('action');

  if (action === 'moveOpportunity') {
    const opportunityId = formData.get('opportunityId') as string;
    const newStage = formData.get('newStage') as OpportunityStage;
    const probability = formData.get('probability') ? parseInt(formData.get('probability') as string) : undefined;

    try {
      await moveOpportunityToStage(opportunityId, userId, newStage, probability);
      return json({ success: true });
    } catch (error) {
      console.error('Error moving opportunity:', error);
      return json({ error: 'Błąd podczas przenoszenia możliwości' }, { status: 400 });
    }
  }

  return json({ error: 'Nieznana akcja' }, { status: 400 });
}

export default function SalesPipelineIndex() {
  const { opportunitiesByStage, stats, error } = useLoaderData<typeof loader>();
  const fetcher = useFetcher();

  const handleMoveOpportunity = (opportunityId: string, newStage: OpportunityStage) => {
    const formData = new FormData();
    formData.append('action', 'moveOpportunity');
    formData.append('opportunityId', opportunityId);
    formData.append('newStage', newStage);
    
    // Set probability based on stage
    const stageProbabilities = {
      NEW_LEAD: 10,
      QUALIFIED: 25,
      PROPOSAL: 50,
      NEGOTIATION: 75,
      IN_PROGRESS: 90,
      CLOSED_WON: 100,
      FOLLOW_UP: 100,
    };
    formData.append('probability', stageProbabilities[newStage].toString());

    fetcher.submit(formData, { method: 'post' });
  };

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">{translations.salesPipeline}</h1>
          <p className="text-gray-600">
            {translations.totalValue}: {stats.totalValue.toFixed(2)} PLN | 
            {translations.conversionRate}: {stats.conversionRate.toFixed(1)}%
          </p>
        </div>
        <Link to="/opportunities/new">
          <Button>{translations.addOpportunity}</Button>
        </Link>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium text-gray-500">Łączne możliwości</h3>
            <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium text-gray-500">{translations.totalValue}</h3>
            <p className="text-2xl font-bold text-green-600">{stats.totalValue.toFixed(0)} PLN</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium text-gray-500">{translations.avgDealSize}</h3>
            <p className="text-2xl font-bold text-blue-600">{stats.avgDealSize.toFixed(0)} PLN</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium text-gray-500">{translations.conversionRate}</h3>
            <p className="text-2xl font-bold text-purple-600">{stats.conversionRate.toFixed(1)}%</p>
          </CardContent>
        </Card>
      </div>

      {/* Pipeline Kanban Board */}
      <div className="grid grid-cols-1 lg:grid-cols-7 gap-4">
        {stageOrder.map((stage) => (
          <div key={stage} className={`rounded-lg border-2 ${stageColors[stage]} p-4`}>
            <div className="mb-4">
              <h3 className="font-semibold text-sm">{translations[stage]}</h3>
              <div className="flex justify-between text-xs text-gray-600 mt-1">
                <span>{opportunitiesByStage[stage].length} możliwości</span>
                <span>
                  {opportunitiesByStage[stage]
                    .reduce((sum, opp) => sum + (opp.value || 0), 0)
                    .toFixed(0)} PLN
                </span>
              </div>
            </div>
            
            <div className="space-y-3">
              {opportunitiesByStage[stage].map((opportunity) => (
                <OpportunityCard 
                  key={opportunity.id} 
                  opportunity={opportunity} 
                  onMove={handleMoveOpportunity}
                  currentStage={stage}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function OpportunityCard({ 
  opportunity, 
  onMove, 
  currentStage 
}: { 
  opportunity: OpportunityWithRelations; 
  onMove: (id: string, stage: OpportunityStage) => void;
  currentStage: OpportunityStage;
}) {
  const [showMoveMenu, setShowMoveMenu] = useState(false);

  const getNextStage = (): OpportunityStage | null => {
    const currentIndex = stageOrder.indexOf(currentStage);
    return currentIndex < stageOrder.length - 1 ? stageOrder[currentIndex + 1] : null;
  };

  const getPrevStage = (): OpportunityStage | null => {
    const currentIndex = stageOrder.indexOf(currentStage);
    return currentIndex > 0 ? stageOrder[currentIndex - 1] : null;
  };

  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm">
          <Link to={`/opportunities/${opportunity.id}`} className="hover:underline">
            {opportunity.name}
          </Link>
        </CardTitle>
        <CardDescription className="text-xs">
          {opportunity.customer?.name}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">
              {opportunity.value?.toFixed(0)} PLN
            </span>
            <Badge variant="outline" className="text-xs">
              {opportunity.probability}%
            </Badge>
          </div>
          
          <div className="text-xs text-gray-600">
            <div>{translations[opportunity.serviceType]}</div>
            <div>{translations[opportunity.buildingType]}</div>
          </div>

          {opportunity.expectedCloseDate && (
            <div className="text-xs text-gray-500">
              {new Date(opportunity.expectedCloseDate).toLocaleDateString('pl-PL')}
            </div>
          )}

          {/* Move buttons */}
          <div className="flex gap-1 mt-2">
            {getPrevStage() && (
              <Button
                size="sm"
                variant="outline"
                className="text-xs px-2 py-1 h-6"
                onClick={() => onMove(opportunity.id, getPrevStage()!)}
              >
                ←
              </Button>
            )}
            {getNextStage() && (
              <Button
                size="sm"
                variant="outline"
                className="text-xs px-2 py-1 h-6"
                onClick={() => onMove(opportunity.id, getNextStage()!)}
              >
                →
              </Button>
            )}
            <Link to={`/opportunities/${opportunity.id}`}>
              <Button
                size="sm"
                variant="outline"
                className="text-xs px-2 py-1 h-6"
              >
                👁
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

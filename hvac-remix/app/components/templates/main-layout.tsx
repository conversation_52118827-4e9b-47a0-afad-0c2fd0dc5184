import type { User } from "@prisma/client";
import { Footer } from "~/components/organisms/footer";
import { Head<PERSON> } from "~/components/organisms/header";
import { Navigation } from "~/components/organisms/navigation";
import { OfflineIndicator, OfflineBanner } from "~/components/ui/offline-indicator";
import type { UserRole, Notification } from "~/types/shared";

interface MainLayoutProps {
  user: User | null;
  children: React.ReactNode;
  userRole?: UserRole;
  notifications?: Notification[];
  onMarkAsRead?: (id: string) => void;
  onMarkAllAsRead?: () => void;
}

export function MainLayout({
  user,
  children,
  userRole = 'USER',
  notifications = [],
  onMarkAsRead,
  onMarkAllAsRead
}: MainLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-background/90">
      {/* Offline Banner */}
      <OfflineBanner />

      {/* Main Grid Layout */}
      <div className="grid min-h-screen grid-rows-[auto_1fr_auto] lg:grid-cols-[280px_1fr]">
        {/* Header - spans full width */}
        <Header
          user={user}
          userRole={userRole}
          notifications={notifications}
          onMarkAsRead={onMarkAsRead}
          onMarkAllAsRead={onMarkAllAsRead}
          className="lg:col-span-2"
        />

        {/* Sidebar Navigation - hidden on mobile, visible on desktop */}
        <aside className="hidden lg:block bg-card/60 backdrop-blur-sm border-r border-border/40 overflow-y-auto">
          <div className="sticky top-0 p-6">
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-foreground/90 mb-4">Nawigacja</h2>
            </div>
            <Navigation userRole={userRole} className="space-y-1" />
          </div>
        </aside>

        {/* Main Content Area */}
        <main className="bg-gradient-to-b from-background/50 to-background/80 backdrop-blur-[1px] overflow-y-auto">
          <div className="container mx-auto px-4 py-6 lg:px-8 lg:py-8">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </div>
        </main>

        {/* Footer - spans full width */}
        <Footer className="lg:col-span-2 border-t border-border/40 bg-card/40 backdrop-blur-sm" />
      </div>

      {/* Mobile Navigation - shows on mobile only */}
      <div className="lg:hidden">
        <Navigation userRole={userRole} />
      </div>

      {/* Offline Indicator */}
      <div className="fixed bottom-4 right-4 z-50">
        <OfflineIndicator />
      </div>
    </div>
  );
}
import { BentoGrid } from "~/components/atoms/bento-grid";
import { BentoCard } from "~/components/molecules/bento-card";
import React from "react";
import TodaySchedule from "~/components/molecules/today-schedule";
import EquipmentStatusIndicator from "~/components/molecules/equipment-status-indicator";
import WeatherImpactWidget from "~/components/molecules/weather-impact-widget";
import PerformanceChart from "~/components/molecules/performance-chart";
import QuickActionGrid from "~/components/molecules/quick-action-grid";
import {
  CalendarIcon,
  CogIcon,
  CloudIcon,
  TrendingUpIcon,
} from "@heroicons/react/24/outline";

export const TechnicianDashboard: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <main className="container mx-auto px-4 py-8">
        <BentoGrid>
          {/* Today's Schedule */}
          <BentoCard
            title="Dzisiejsze Zlecenia"
            value="5"
            icon={<CalendarIcon className="h-5 w-5" />}
            size="wide"
          >
            <TodaySchedule />
          </BentoCard>

          {/* Equipment Status */}
          <BentoCard
            title="Status Sprzętu"
            icon={<CogIcon className="h-5 w-5" />}
          >
            <EquipmentStatusIndicator />
          </BentoCard>

          {/* Weather Impact */}
          <BentoCard
            title="Wpływ Pogody"
            icon={<CloudIcon className="h-5 w-5" />}
          >
            <WeatherImpactWidget />
          </BentoCard>

          {/* Performance Metrics */}
          <BentoCard
            title="Wydajność"
            value="94%"
            trend={2.5}
            icon={<TrendingUpIcon className="h-5 w-5" />}
            size="tall"
          >
            <PerformanceChart />
          </BentoCard>

          {/* Quick Actions */}
          <BentoCard title="Szybkie Akcje" size="wide">
            <QuickActionGrid />
          </BentoCard>
        </BentoGrid>
      </main>
    </div>
  );
};
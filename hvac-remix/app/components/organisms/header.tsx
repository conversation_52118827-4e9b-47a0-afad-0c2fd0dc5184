import type { User } from "@prisma/client";
import { <PERSON> } from "@remix-run/react";
import { ModeToggle } from "~/components/mode-toggle";
import { NotificationCenter } from "~/components/organisms/notification-center";
import { Button } from "~/components/ui/button";
import type { UserRole, Notification } from "~/types/shared";

interface HeaderProps {
  user: User | null;
  userRole: UserRole;
  notifications?: Notification[];
  onMarkAsRead?: (id: string) => void;
  onMarkAllAsRead?: () => void;
  className?: string;
}

export function Header({
  user,
  userRole,
  notifications = [],
  onMarkAsRead,
  onMarkAllAsRead,
  className
}: HeaderProps) {
  return (
    <header className={`sticky top-0 z-40 w-full border-b border-border/40 bg-card/80 backdrop-blur-md supports-[backdrop-filter]:bg-card/60 shadow-sm ${className}`}>
      <div className="container mx-auto px-4 lg:px-6 py-3">
        <div className="flex h-16 items-center justify-between">
          {/* <PERSON><PERSON> and Brand */}
          <div className="flex items-center gap-3">
            <Link to="/" className="flex items-center gap-3 group">
              <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                <span className="text-white font-bold text-lg">H</span>
              </div>
              <div className="hidden sm:block">
                <span className="text-xl font-heading font-bold text-gradient bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                  HVAC CRM
                </span>
                <div className="text-xs text-muted-foreground font-medium">
                  Servicetool Pro
                </div>
              </div>
            </Link>
          </div>

          {/* User menu and controls */}
          <div className="flex items-center gap-3">
            {/* Notifications center */}
            {user && (
              <NotificationCenter
                notifications={notifications}
                onMarkAsRead={onMarkAsRead}
                onMarkAllAsRead={onMarkAllAsRead}
              />
            )}

            {/* Theme toggle */}
            <ModeToggle />

            {/* User menu */}
            {user ? (
              <div className="relative group">
                <button
                  type="button"
                  className="flex items-center gap-2 text-sm font-medium transition-all duration-200 hover:scale-105"
                  aria-expanded="false"
                >
                  <span className="sr-only">Open user menu</span>
                  <div className="h-10 w-10 rounded-full bg-gradient-to-br from-primary/20 to-accent/20 ring-2 ring-primary/30 flex items-center justify-center text-primary font-semibold shadow-md hover:shadow-lg transition-all duration-200">
                    {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
                  </div>
                  <div className="hidden lg:block text-left">
                    <div className="font-medium text-foreground">{user.name || user.email}</div>
                    <div className="text-xs text-muted-foreground">{userRole}</div>
                  </div>
                </button>
                <div className="hidden group-hover:block absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-xl bg-card/95 backdrop-blur-md py-2 shadow-xl ring-1 ring-border/50 focus:outline-none transition-all duration-200 animate-in fade-in-50 slide-in-from-top-5">
                  <div className="px-4 py-3 text-sm border-b border-border/50">
                    <div className="font-medium text-foreground">{user.name || user.email}</div>
                    <div className="text-xs text-muted-foreground">{userRole}</div>
                  </div>
                  <Link
                    to="/settings"
                    className="block px-4 py-2 text-sm hover:bg-accent/10 hover:text-accent transition-colors rounded-lg mx-2 my-1"
                  >
                    Ustawienia
                  </Link>
                  <Link
                    to="/logout"
                    className="block px-4 py-2 text-sm hover:bg-destructive/10 hover:text-destructive transition-colors rounded-lg mx-2 my-1"
                  >
                    Wyloguj
                  </Link>
                </div>
              </div>
            ) : (
              <Button asChild variant="default" size="sm" className="shadow-md hover:shadow-lg transition-all duration-200">
                <Link to="/login">Zaloguj</Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
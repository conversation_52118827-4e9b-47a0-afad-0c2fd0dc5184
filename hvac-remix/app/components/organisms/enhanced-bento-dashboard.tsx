import React from "react";
import { 
  <PERSON>, 
  <PERSON>, 
  <PERSON>ch, 
  <PERSON><PERSON><PERSON><PERSON>p, 
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Clock,
  DollarSign,
  MapPin,
  Thermometer,
  Zap,
  Settings
} from "lucide-react";
import { BentoGrid } from "~/components/atoms/bento-grid";
import { BentoCard } from "~/components/molecules/bento-card";

interface DashboardData {
  todayAppointments: number;
  activeCustomers: number;
  pendingOrders: number;
  monthlyRevenue: number;
  equipmentAlerts: number;
  completedToday: number;
  avgResponseTime: string;
  weatherTemp: number;
  energyEfficiency: number;
  systemHealth: number;
}

interface EnhancedBentoDashboardProps {
  data: DashboardData;
  userRole?: 'admin' | 'manager' | 'technician' | 'dispatcher';
}

export const EnhancedBentoDashboard: React.FC<EnhancedBentoDashboardProps> = ({
  data,
  userRole = 'technician'
}) => {
  const handleCardClick = (cardType: string) => {
    console.log(`Clicked on ${cardType} card`);
    // Navigate to specific page or open modal
  };

  return (
    <div className="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Dashboard HVAC CRM
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Przegląd kluczowych metryk i aktywności
        </p>
      </div>

      <BentoGrid>
        {/* Today's Appointments - Wide Card */}
        <BentoCard
          title="Dzisiejsze Wizyty"
          value={data.todayAppointments}
          icon={<Calendar className="h-6 w-6" />}
          size="wide"
          onClick={() => handleCardClick('appointments')}
        >
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Zaplanowane</span>
              <span className="font-medium">{data.todayAppointments}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Ukończone</span>
              <span className="font-medium text-green-600">{data.completedToday}</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(data.completedToday / data.todayAppointments) * 100}%` }}
              />
            </div>
          </div>
        </BentoCard>

        {/* Active Customers */}
        <BentoCard
          title="Aktywni Klienci"
          value={data.activeCustomers}
          trend={5.2}
          icon={<Users className="h-6 w-6" />}
          onClick={() => handleCardClick('customers')}
        />

        {/* Equipment Alerts */}
        <BentoCard
          title="Alerty Sprzętu"
          value={data.equipmentAlerts}
          icon={<AlertTriangle className="h-6 w-6" />}
          onClick={() => handleCardClick('alerts')}
        >
          <div className="mt-2">
            <span className="text-xs text-orange-600 dark:text-orange-400 font-medium">
              Wymaga uwagi
            </span>
          </div>
        </BentoCard>

        {/* Monthly Revenue - Tall Card */}
        <BentoCard
          title="Przychód Miesięczny"
          value={`${data.monthlyRevenue.toLocaleString('pl-PL')} zł`}
          trend={12.5}
          icon={<DollarSign className="h-6 w-6" />}
          size="tall"
          onClick={() => handleCardClick('revenue')}
        >
          <div className="mt-4 space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Cel miesięczny</span>
              <span className="font-medium">85%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div className="bg-green-600 h-2 rounded-full w-[85%]" />
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Pozostało: 4 dni
            </div>
          </div>
        </BentoCard>

        {/* Weather Impact */}
        <BentoCard
          title="Wpływ Pogody"
          value={`${data.weatherTemp}°C`}
          icon={<Thermometer className="h-6 w-6" />}
          onClick={() => handleCardClick('weather')}
        >
          <div className="mt-2">
            <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
              Optymalne warunki
            </span>
          </div>
        </BentoCard>

        {/* Response Time */}
        <BentoCard
          title="Czas Reakcji"
          value={data.avgResponseTime}
          icon={<Clock className="h-6 w-6" />}
          onClick={() => handleCardClick('response-time')}
        >
          <div className="mt-2">
            <span className="text-xs text-green-600 dark:text-green-400 font-medium">
              Poniżej celu
            </span>
          </div>
        </BentoCard>

        {/* System Health - Large Card */}
        <BentoCard
          title="Kondycja Systemu"
          value={`${data.systemHealth}%`}
          icon={<Settings className="h-6 w-6" />}
          size="large"
          onClick={() => handleCardClick('system-health')}
        >
          <div className="mt-4 grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">98%</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Uptime</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">2.1s</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Avg Response</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">156</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">API Calls/min</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">12GB</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Storage Used</div>
            </div>
          </div>
        </BentoCard>

        {/* Energy Efficiency */}
        <BentoCard
          title="Efektywność Energetyczna"
          value={`${data.energyEfficiency}%`}
          trend={3.1}
          icon={<Zap className="h-6 w-6" />}
          onClick={() => handleCardClick('energy')}
        />

        {/* Pending Orders */}
        <BentoCard
          title="Oczekujące Zlecenia"
          value={data.pendingOrders}
          icon={<Wrench className="h-6 w-6" />}
          onClick={() => handleCardClick('pending-orders')}
        >
          <div className="mt-2">
            <span className="text-xs text-yellow-600 dark:text-yellow-400 font-medium">
              Do przydzielenia
            </span>
          </div>
        </BentoCard>

        {/* Quick Actions - Wide Card */}
        <BentoCard
          title="Szybkie Akcje"
          icon={<TrendingUp className="h-6 w-6" />}
          size="wide"
        >
          <div className="grid grid-cols-2 gap-3 mt-4">
            <button className="flex items-center justify-center gap-2 p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors">
              <Calendar className="h-4 w-4" />
              <span className="text-sm font-medium">Nowe Zlecenie</span>
            </button>
            <button className="flex items-center justify-center gap-2 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors">
              <Users className="h-4 w-4" />
              <span className="text-sm font-medium">Dodaj Klienta</span>
            </button>
            <button className="flex items-center justify-center gap-2 p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors">
              <MapPin className="h-4 w-4" />
              <span className="text-sm font-medium">Optymalizuj Trasę</span>
            </button>
            <button className="flex items-center justify-center gap-2 p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg hover:bg-orange-200 dark:hover:bg-orange-900/50 transition-colors">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm font-medium">Raport Dnia</span>
            </button>
          </div>
        </BentoCard>
      </BentoGrid>
    </div>
  );
};

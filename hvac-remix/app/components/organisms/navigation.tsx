import { NavLink } from "@remix-run/react";
import type { UserRole } from "~/types/shared";

interface NavLinkItem {
  name: string;
  to: string;
  icon: React.ReactNode;
}

interface NavigationProps {
  userRole: UserRole;
  className?: string;
}

// I<PERSON>y <PERSON> (uproszczone SVG)
const DashboardIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
  </svg>
);

const SearchIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
  </svg>
);

const CalendarIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
  </svg>
);

const CustomersIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
  </svg>
);

const DevicesIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clipRule="evenodd" />
  </svg>
);

const PredictiveMaintenanceIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
  </svg>
);

const InventoryIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z" />
    <path fillRule="evenodd" d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clipRule="evenodd" />
  </svg>
);

const ServiceOrdersIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
    <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
  </svg>
);

const InvoicesIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
  </svg>
);

const ReportsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
  </svg>
);

const VisualizationsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
:start_line:81
-------
  </svg>
);

const AnalyticsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
  </svg>
);

const OffersIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M5 2a2 2 0 00-2 2v14l3.5-2 3.5 2 3.5-2 3.5 2V4a2 2 0 00-2-2H5zm4.707 3.707a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L8.414 9H10a3 3 0 013 3v1a1 1 0 102 0v-1a5 5 0 00-5-5H8.414l1.293-1.293z" clipRule="evenodd" />
  </svg>
);

const SettingsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
  </svg>
);

const UsersIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
  </svg>
);

const TeamIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
  </svg>
);

// Definicje linków nawigacyjnych dla każdej roli
const managerLinks: NavLinkItem[] = [
  { name: "Dashboard", to: "/dashboard", icon: <DashboardIcon /> },
  { name: "Team Schedule", to: "/calendar", icon: <TeamIcon /> },
  { name: "Customers", to: "/customers", icon: <CustomersIcon /> },
  { name: "GoSpine Customers", to: "/customers/go-spine", icon: <CustomersIcon /> },
  { name: "Devices", to: "/devices", icon: <DevicesIcon /> },
  { name: "Predictive Maintenance", to: "/devices?view=predictions", icon: <PredictiveMaintenanceIcon /> },
  { name: "Service Orders", to: "/service-orders", icon: <ServiceOrdersIcon /> },
  { name: "Inventory", to: "/inventory", icon: <InventoryIcon /> },
  { name: "Offers", to: "/offers", icon: <OffersIcon /> },
  { name: "Invoices", to: "/invoices", icon: <InvoicesIcon /> },
  { name: "Reports", to: "/service-reports", icon: <ReportsIcon /> },
  { name: "Visualizations", to: "/visualizations", icon: <VisualizationsIcon /> },
  { name: "Python Mixer Analytics", to: "/analytics/python-mixer", icon: <AnalyticsIcon /> }, // Added
  { name: "Search", to: "/search", icon: <SearchIcon /> },
];

const technicianLinks: NavLinkItem[] = [
  { name: "Dashboard", to: "/dashboard", icon: <DashboardIcon /> },
  { name: "My Schedule", to: "/calendar", icon: <CalendarIcon /> },
  { name: "Active Orders", to: "/service-orders?status=IN_PROGRESS", icon: <ServiceOrdersIcon /> },
  { name: "Service Reports", to: "/service-reports", icon: <ReportsIcon /> },
  { name: "Devices", to: "/devices", icon: <DevicesIcon /> },
  { name: "Parts", to: "/inventory/parts", icon: <InventoryIcon /> },
  { name: "Predictive Maintenance", to: "/devices?view=predictions", icon: <PredictiveMaintenanceIcon /> },
  { name: "Search", to: "/search", icon: <SearchIcon /> },
];

const administratorLinks: NavLinkItem[] = [
  { name: "Dashboard", to: "/dashboard", icon: <DashboardIcon /> },
  { name: "Users", to: "/users", icon: <UsersIcon /> },
  { name: "Customers", to: "/customers", icon: <CustomersIcon /> },
  { name: "GoSpine Customers", to: "/customers/go-spine", icon: <CustomersIcon /> },
  { name: "Devices", to: "/devices", icon: <DevicesIcon /> },
  { name: "Predictive Maintenance", to: "/devices?view=predictions", icon: <PredictiveMaintenanceIcon /> },
  { name: "Service Orders", to: "/service-orders", icon: <ServiceOrdersIcon /> },
  { name: "Inventory", to: "/inventory", icon: <InventoryIcon /> },
  { name: "Offers", to: "/offers", icon: <OffersIcon /> },
  { name: "Offer Templates", to: "/offer-templates", icon: <OffersIcon /> },
  { name: "Invoices", to: "/invoices", icon: <InvoicesIcon /> },
  { name: "Calendar", to: "/calendar", icon: <CalendarIcon /> },
  { name: "Reports", to: "/service-reports", icon: <ReportsIcon /> },
  { name: "Visualizations", to: "/visualizations", icon: <VisualizationsIcon /> },
  { name: "Python Mixer Analytics", to: "/analytics/python-mixer", icon: <AnalyticsIcon /> }, // Added
  { name: "Search", to: "/search", icon: <SearchIcon /> },
  { name: "Settings", to: "/settings", icon: <SettingsIcon /> },
];

const customerLinks: NavLinkItem[] = [
  { name: "Dashboard", to: "/customer-portal", icon: <DashboardIcon /> },
  { name: "My Devices", to: "/customer-portal/devices", icon: <DevicesIcon /> },
  { name: "Service Orders", to: "/customer-portal/service-orders", icon: <ServiceOrdersIcon /> },
  { name: "Invoices", to: "/customer-portal/invoices", icon: <InvoicesIcon /> },
  { name: "Request Service", to: "/customer-portal/request-service", icon: <ServiceOrdersIcon /> },
];

const userLinks: NavLinkItem[] = [
  { name: "Dashboard", to: "/dashboard", icon: <DashboardIcon /> },
  { name: "Customers", to: "/customers", icon: <CustomersIcon /> },
  { name: "GoSpine Customers", to: "/customers/go-spine", icon: <CustomersIcon /> },
  { name: "Devices", to: "/devices", icon: <DevicesIcon /> },
  { name: "Predictive Maintenance", to: "/devices?view=predictions", icon: <PredictiveMaintenanceIcon /> },
  { name: "Service Orders", to: "/service-orders", icon: <ServiceOrdersIcon /> },
  { name: "Inventory", to: "/inventory", icon: <InventoryIcon /> },
  { name: "Offers", to: "/offers", icon: <OffersIcon /> },
  { name: "Calendar", to: "/calendar", icon: <CalendarIcon /> },
  { name: "Visualizations", to: "/visualizations", icon: <VisualizationsIcon /> },
  { name: "Python Mixer Analytics", to: "/analytics/python-mixer", icon: <AnalyticsIcon /> }, // Added
  { name: "Search", to: "/search", icon: <SearchIcon /> },
];

// Funkcja pomocnicza do wyboru odpowiednich linków na podstawie roli
function getLinksByRole(role: UserRole): NavLinkItem[] {
  switch (role) {
    case 'MANAGER':
      return managerLinks;
    case 'TECHNICIAN':
      return technicianLinks;
    case 'ADMIN':
      return administratorLinks;
    case 'CUSTOMER':
      return customerLinks;
    case 'USER':
    default:
      return userLinks;
  }
}

export function Navigation({ userRole, className = "" }: NavigationProps) {
  const links = getLinksByRole(userRole);

  return (
    <>
      {/* Desktop Sidebar Navigation */}
      <nav className={`hidden lg:block space-y-1 ${className}`}>
        {links.map((link, index) => (
          <NavLink
            key={link.to}
            to={link.to}
            className={({ isActive }) =>
              `group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 animate-fade-in hover-lift ${
                isActive
                  ? "bg-primary/15 text-primary border-l-3 border-primary shadow-sm"
                  : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground hover:translate-x-1"
              }`
            }
            style={{ animationDelay: `${index * 30}ms` }}
          >
            <span className="mr-3 transition-transform duration-200 group-hover:scale-110 flex-shrink-0">
              {link.icon}
            </span>
            <span className="truncate">{link.name}</span>
          </NavLink>
        ))}
      </nav>

      {/* Mobile Bottom Navigation */}
      <nav className="lg:hidden fixed bottom-0 left-0 right-0 bg-card/95 backdrop-blur-md border-t border-border z-50 shadow-lg">
        <div className="flex justify-around items-center py-2 px-1 max-w-md mx-auto">
          {links.slice(0, 5).map((link, index) => (
            <NavLink
              key={link.to}
              to={link.to}
              className={({ isActive }) =>
                `flex flex-col items-center justify-center p-2 min-w-[60px] rounded-lg transition-all duration-200 touch-manipulation ${
                  isActive
                    ? "text-primary bg-primary/10 scale-105"
                    : "text-muted-foreground hover:text-primary active:scale-95"
                }`
              }
            >
              <span className={`transition-all duration-200 ${
                index === 0 ? 'mb-1' : 'mb-0.5'
              }`}>
                {link.icon}
              </span>
              <span className="text-xs font-medium truncate max-w-[50px]">
                {link.name.split(' ')[0]}
              </span>
            </NavLink>
          ))}
        </div>
      </nav>

      {/* Mobile Drawer for Additional Links */}
      {links.length > 5 && (
        <div className="lg:hidden">
          {/* TODO: Implement drawer/modal for additional navigation items */}
        </div>
      )}
    </>
  );
}
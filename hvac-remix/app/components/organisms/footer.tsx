import { Link } from "@remix-run/react";

export function Footer({ className }: { className?: string }) {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={`border-t border-border/40 bg-card/60 backdrop-blur-sm ${className}`}>
      <div className="container mx-auto px-4 lg:px-6 py-6">
        <div className="flex flex-col lg:flex-row justify-between items-center gap-4">
          <div className="flex flex-col items-center lg:items-start gap-1">
            <div className="font-heading font-bold text-lg text-gradient bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              HVAC CRM Servicetool
            </div>
            <span className="text-xs text-muted-foreground">
              &copy; {currentYear} Fulmark HVAC. Wszystkie prawa zastrzeżone.
            </span>
          </div>

          <div className="flex flex-wrap justify-center lg:justify-end gap-6">
            <Link
              to="/about"
              className="text-xs text-muted-foreground hover:text-accent transition-colors duration-200 hover:underline"
            >
              O nas
            </Link>
            <Link
              to="/privacy"
              className="text-xs text-muted-foreground hover:text-accent transition-colors duration-200 hover:underline"
            >
              Prywatność
            </Link>
            <Link
              to="/terms"
              className="text-xs text-muted-foreground hover:text-accent transition-colors duration-200 hover:underline"
            >
              Regulamin
            </Link>
            <Link
              to="/contact"
              className="text-xs text-muted-foreground hover:text-accent transition-colors duration-200 hover:underline"
            >
              Kontakt
            </Link>
            <Link
              to="/help"
              className="text-xs text-muted-foreground hover:text-accent transition-colors duration-200 hover:underline"
            >
              Pomoc
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
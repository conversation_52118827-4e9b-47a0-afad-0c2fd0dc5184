import { twMerge } from "tailwind-merge";
import React from "react";

interface BentoGridProps {
  children: React.ReactNode;
  className?: string;
}

export const BentoGrid: React.FC<BentoGridProps> = ({ children, className }) => {
  return (
    <div className={twMerge(
      "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-6 auto-rows-min",
      className
    )}>
      {children}
    </div>
  );
};

import { motion } from "framer-motion";
import { twMerge } from "tailwind-merge";
import React from "react";

interface BentoCardProps {
  title: string;
  value?: string;
  trend?: number;
  icon?: React.ReactNode;
  size?: "normal" | "wide" | "tall" | "large";
  children: React.ReactNode;
  className?: string;
}

export const BentoCard: React.FC<BentoCardProps> = ({
  title,
  value,
  trend,
  icon,
  size = "normal",
  children,
  className,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={twMerge(
        "group relative overflow-hidden rounded-xl",
        "bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800",
        "border border-gray-200 dark:border-gray-700",
        "hover:shadow-lg hover:shadow-blue-500/10 dark:hover:shadow-blue-400/10",
        "transition-all duration-300 hover:scale-[1.02]",
        size === "normal" && "col-span-1",
        size === "wide" && "col-span-2",
        size === "tall" && "col-span-1 row-span-2",
        size === "large" && "col-span-2 row-span-2",
        className
      )}
    >
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <div className="relative p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {title}
          </h3>
          {icon && (
            <div className="text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300">
              {icon}
            </div>
          )}
        </div>

        {value && (
          <div className="mb-4">
            <span className="text-3xl font-bold text-gray-900 dark:text-white">
              {value}
            </span>
            {trend && (
              <motion.span
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className={twMerge(
                  "ml-2 text-sm font-medium",
                  trend > 0
                    ? "text-green-600 dark:text-green-400"
                    : "text-red-600 dark:text-red-400"
                )}
              >
                {trend > 0 ? "↗" : "↘"} {Math.abs(trend)}%
              </motion.span>
            )}
          </div>
        )}

        {children}
      </div>
    </motion.div>
  );
};

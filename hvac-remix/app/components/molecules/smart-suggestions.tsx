import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { cn } from "~/lib/utils";

export interface AISuggestion {
  text: string;
  category: string;
  confidence: number;
  metadata?: Record<string, any>;
}

export interface SmartSuggestionsProps {
  context: string;
  onSuggestionSelect: (suggestion: AISuggestion) => void;
  className?: string;
  type?: 'hvac_service' | 'customer_notes' | 'equipment_issue';
  language?: 'pl' | 'en';
}

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export const SmartSuggestions: React.FC<SmartSuggestionsProps> = ({
  context,
  onSuggestionSelect,
  className,
  type = 'hvac_service',
  language = 'pl'
}) => {
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchSuggestions = useCallback(
    debounce(async (text: string) => {
      if (!text || text.length < 3) {
        setSuggestions([]);
        return;
      }
      
      setLoading(true);
      try {
        const response = await fetch('/api/ai/suggestions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            context: text,
            type,
            language
          })
        });
        
        if (response.ok) {
          const data = await response.json();
          setSuggestions(data.suggestions || []);
        } else {
          // Fallback suggestions for demo purposes
          setSuggestions([
            {
              text: "Sprawdzenie poziomu czynnika chłodniczego",
              category: "Diagnostyka",
              confidence: 0.9
            },
            {
              text: "Czyszczenie filtrów powietrza",
              category: "Konserwacja",
              confidence: 0.85
            },
            {
              text: "Kontrola działania wentylatora",
              category: "Diagnostyka",
              confidence: 0.8
            }
          ]);
        }
      } catch (error) {
        console.error('AI suggestions error:', error);
        setSuggestions([]);
      } finally {
        setLoading(false);
      }
    }, 300),
    [type, language]
  );

  useEffect(() => {
    fetchSuggestions(context);
  }, [context, fetchSuggestions]);

  if (!suggestions.length && !loading) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className={cn(
          "absolute top-full left-0 right-0 mt-2 z-50",
          "bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700",
          "max-h-60 overflow-y-auto",
          className
        )}
      >
        <div className="p-2">
          {loading ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
              <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                Analizuję z AI...
              </span>
            </div>
          ) : (
            <div className="space-y-1">
              {suggestions.map((suggestion, index) => (
                <motion.button
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors group"
                  onClick={() => onSuggestionSelect(suggestion)}
                >
                  <div className="flex items-start">
                    <Sparkles className="h-4 w-4 text-blue-500 mr-2 mt-0.5 group-hover:text-blue-600" />
                    <div className="flex-1">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {suggestion.text}
                      </span>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {suggestion.category}
                        </span>
                        <div className="flex items-center">
                          <div className={cn(
                            "w-2 h-2 rounded-full mr-1",
                            suggestion.confidence > 0.8 ? "bg-green-500" :
                            suggestion.confidence > 0.6 ? "bg-yellow-500" : "bg-red-500"
                          )} />
                          <span className="text-xs text-gray-500">
                            {Math.round(suggestion.confidence * 100)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

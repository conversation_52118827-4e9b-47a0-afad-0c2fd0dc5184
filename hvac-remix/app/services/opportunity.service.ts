// 🎯 Opportunity Service - AI-Powered Sales Pipeline Management
// Claude 4 w Augment Framework - DIVINE QUALITY! 💎

import type {
  Opportunity,
  OpportunityStage
} from "~/models/opportunity";
import {
  getOpportunities,
  getOpportunity,
  createOpportunity,
  updateOpportunity,
  deleteOpportunity,
  moveOpportunityToStage,
  type OpportunityWithRelations,
  type CreateOpportunityData,
  type UpdateOpportunityData
} from "~/models/opportunity.server";

export class OpportunityService {
  
  // 🚀 CRUD Operations with AI Enhancement
  
  static async list(filters?: {
    stage?: OpportunityStage;
    ownerId?: string;
    customerId?: string;
    source?: string;
    minValue?: number;
    maxValue?: number;
    dateRange?: { from: Date; to: Date };
    search?: string;
    limit?: number;
    offset?: number;
  }) {
    try {
      // Convert filters to the format expected by the server function
      const searchFilters = {
        search: filters?.search,
        stage: filters?.stage,
        ownerId: filters?.ownerId,
        customerId: filters?.customerId,
        valueMin: filters?.minValue,
        valueMax: filters?.maxValue,
        expectedCloseDateFrom: filters?.dateRange?.from,
        expectedCloseDateTo: filters?.dateRange?.to,
      };

      // For now, we'll use a dummy userId - this should come from session
      const userId = "user-1"; // TODO: Get from session
      const page = filters?.offset ? Math.floor(filters.offset / (filters.limit || 20)) + 1 : 1;
      const limit = filters?.limit || 20;

      const { opportunities } = await getOpportunities(userId, searchFilters, page, limit);

      // Enhance with AI insights
      const enhancedOpportunities = await Promise.all(
        opportunities.map(async (opp) => ({
          ...opp,
          aiInsights: await this.generateAIInsights(opp),
          recommendedActions: await this.getRecommendedActions(opp)
        }))
      );

      return enhancedOpportunities;
    } catch (error) {
      console.error('Error listing opportunities:', error);
      throw new Error('Failed to fetch opportunities');
    }
  }

  static async get(id: string) {
    try {
      // For now, we'll use a dummy userId - this should come from session
      const userId = "user-1"; // TODO: Get from session
      const opportunity = await getOpportunity(id, userId);

      if (!opportunity) {
        throw new Error('Opportunity not found');
      }

      // Enhance with comprehensive AI analysis
      const [aiInsights, leadScore, winProbability] = await Promise.all([
        this.generateAIInsights(opportunity),
        this.calculateLeadScore(opportunity),
        this.predictWinProbability(opportunity)
      ]);

      return {
        ...opportunity,
        aiInsights,
        leadScore,
        winProbabilityAI: winProbability,
        recommendedActions: await this.getRecommendedActions(opportunity)
      };
    } catch (error) {
      console.error('Error fetching opportunity:', error);
      throw new Error('Failed to fetch opportunity');
    }
  }

  static async create(data: Partial<Opportunity>) {
    try {
      // Calculate initial AI scores
      const leadScore = await this.calculateLeadScore(data as Opportunity);
      const winProbability = await this.predictWinProbability(data as Opportunity);

      const opportunityData: CreateOpportunityData = {
        name: data.name || 'New Opportunity',
        description: data.description,
        stage: data.stage || 'NEW_LEAD',
        probability: winProbability,
        value: data.value || 0,
        currency: data.currency || 'PLN',
        expectedCloseDate: data.expectedCloseDate,
        serviceType: data.serviceType,
        equipmentType: data.equipmentType,
        installationComplexity: data.installationComplexity,
        roomCount: data.roomCount,
        totalArea: data.totalArea,
        buildingType: data.buildingType,
        ownerId: data.ownerId || 'user-1', // TODO: Get from session
        teamId: data.teamId,
        leadSource: data.leadSource,
        referralSource: data.referralSource,
        customerId: data.customerId || 'customer-1' // TODO: Handle properly
      };

      const opportunity = await createOpportunity(opportunityData);

      if (!opportunity) {
        throw new Error('Failed to create opportunity');
      }

      // Trigger automation workflows
      await this.triggerAutomationWorkflows(opportunity, 'created');

      return opportunity;
    } catch (error) {
      console.error('Error creating opportunity:', error);
      throw new Error('Failed to create opportunity');
    }
  }

  static async update(id: string, data: Partial<Opportunity>) {
    try {
      const userId = "user-1"; // TODO: Get from session
      const currentOpportunity = await this.get(id);

      // Recalculate AI scores if relevant fields changed
      let updates: UpdateOpportunityData = {
        name: data.name,
        description: data.description,
        stage: data.stage,
        probability: data.probability,
        value: data.value,
        currency: data.currency,
        expectedCloseDate: data.expectedCloseDate,
        actualCloseDate: data.actualCloseDate,
        serviceType: data.serviceType,
        equipmentType: data.equipmentType,
        installationComplexity: data.installationComplexity,
        roomCount: data.roomCount,
        totalArea: data.totalArea,
        buildingType: data.buildingType,
        ownerId: data.ownerId,
        teamId: data.teamId,
        leadSource: data.leadSource,
        referralSource: data.referralSource
      };

      if (this.shouldRecalculateScores(data)) {
        const leadScore = await this.calculateLeadScore({ ...currentOpportunity, ...data });
        const winProbability = await this.predictWinProbability({ ...currentOpportunity, ...data });

        updates = {
          ...updates,
          probability: winProbability
        };
      }

      const opportunity = await updateOpportunity(id, userId, updates);

      if (!opportunity) {
        throw new Error('Failed to update opportunity');
      }

      // Trigger automation workflows if stage changed
      if (data.stage && data.stage !== currentOpportunity.stage) {
        await this.triggerAutomationWorkflows(opportunity, 'stage_changed');
      }

      return opportunity;
    } catch (error) {
      console.error('Error updating opportunity:', error);
      throw new Error('Failed to update opportunity');
    }
  }

  static async delete(id: string) {
    try {
      const userId = "user-1"; // TODO: Get from session
      return await deleteOpportunity(id, userId);
    } catch (error) {
      console.error('Error deleting opportunity:', error);
      throw new Error('Failed to delete opportunity');
    }
  }

  // 🧠 AI-Powered Lead Scoring
  
  static async calculateLeadScore(opportunity: Opportunity): Promise<number> {
    try {
      // Simplified lead scoring for now
      let score = 50; // Base score

      // Value-based scoring
      if (opportunity.value > 50000) score += 20;
      else if (opportunity.value > 20000) score += 10;
      else if (opportunity.value > 10000) score += 5;

      // Service type scoring
      if (opportunity.serviceType === 'INSTALLATION') score += 15;
      else if (opportunity.serviceType === 'MAINTENANCE') score += 10;
      else if (opportunity.serviceType === 'EMERGENCY') score += 25;

      // Building type scoring
      if (opportunity.buildingType === 'OFFICE') score += 10;
      else if (opportunity.buildingType === 'INDUSTRIAL') score += 15;

      return Math.round(Math.min(100, Math.max(0, score)));
    } catch (error) {
      console.error('Error calculating lead score:', error);
      return 50; // Default score
    }
  }

  // Simplified AI insights for now
  static async generateAIInsights(opportunity: OpportunityWithRelations): Promise<string[]> {
    const insights: string[] = [];

    if (opportunity.value > 30000) {
      insights.push('Wysokowartościowa możliwość - priorytet w obsłudze');
    }

    if (opportunity.serviceType === 'EMERGENCY') {
      insights.push('Pilna naprawa - szybka reakcja może zwiększyć zadowolenie klienta');
    }

    if (opportunity.buildingType === 'OFFICE') {
      insights.push('Biuro - potencjał dla regularnych kontraktów serwisowych');
    }

    return insights;
  }

  // 📊 Win Probability Prediction
  
  static async predictWinProbability(opportunity: Opportunity): Promise<number> {
    try {
      // Simplified win probability calculation
      let baseProbability = this.getDefaultProbabilityByStage(opportunity.stage);

      // Adjust based on value
      if (opportunity.value > 50000) baseProbability += 5;
      else if (opportunity.value < 5000) baseProbability -= 5;

      // Adjust based on service type
      if (opportunity.serviceType === 'EMERGENCY') baseProbability += 10;
      else if (opportunity.serviceType === 'MAINTENANCE') baseProbability += 5;

      return Math.min(100, Math.max(0, baseProbability));
    } catch (error) {
      console.error('Error predicting win probability:', error);
      return this.getDefaultProbabilityByStage(opportunity.stage);
    }
  }

  // 🎯 Recommended Actions
  
  static async getRecommendedActions(opportunity: Opportunity): Promise<string[]> {
    const actions: string[] = [];

    // Stage-specific recommendations
    switch (opportunity.stage) {
      case 'NEW_LEAD':
        actions.push('Zaplanuj rozmowę kwalifikacyjną');
        actions.push('Wyślij e-mail powitalny z informacjami o firmie');
        break;
      case 'QUALIFIED':
        actions.push('Przeprowadź analizę potrzeb');
        actions.push('Zaplanuj wizytę na miejscu jeśli wymagana');
        break;
      case 'PROPOSAL':
        actions.push('Przygotuj szczegółową ofertę');
        actions.push('Oblicz dokładną wycenę');
        break;
      case 'NEGOTIATION':
        actions.push('Kontynuuj negocjacje oferty');
        actions.push('Odpowiedz na wszelkie wątpliwości');
        break;
      case 'IN_PROGRESS':
        actions.push('Sfinalizuj warunki umowy');
        actions.push('Przygotuj kontrakt');
        break;
      case 'CLOSED_WON':
        actions.push('Rozpocznij realizację projektu');
        actions.push('Zaplanuj harmonogram prac');
        break;
      case 'FOLLOW_UP':
        actions.push('Przeprowadź ankietę satysfakcji');
        actions.push('Zaplanuj przyszłe usługi serwisowe');
        break;
    }

    // Simple AI-powered recommendations
    if (opportunity.value > 30000) {
      actions.push('Przypisz doświadczonego sprzedawcę');
    }

    if (opportunity.serviceType === 'EMERGENCY') {
      actions.push('Priorytetowa obsługa - kontakt w ciągu 2h');
    }

    return actions;
  }

  // 🔄 Pipeline Management

  static async moveToNextStage(opportunityId: string): Promise<Opportunity> {
    const opportunity = await this.get(opportunityId);
    const nextStage = this.getNextStage(opportunity.stage);

    if (!nextStage) {
      throw new Error('Opportunity is already in final stage');
    }

    return this.update(opportunityId, {
      stage: nextStage,
      probability: this.getDefaultProbabilityByStage(nextStage)
    });
  }

  static async moveToPreviousStage(opportunityId: string): Promise<Opportunity> {
    const opportunity = await this.get(opportunityId);
    const previousStage = this.getPreviousStage(opportunity.stage);

    if (!previousStage) {
      throw new Error('Opportunity is already in first stage');
    }

    return this.update(opportunityId, {
      stage: previousStage,
      probability: this.getDefaultProbabilityByStage(previousStage)
    });
  }

  // 🤖 Automation & Workflows
  
  static async triggerAutomationWorkflows(opportunity: Opportunity, trigger: string) {
    try {
      // Simplified automation for now - just log the trigger
      console.log(`Automation triggered: ${trigger} for opportunity ${opportunity.id}`);

      // TODO: Implement actual workflow triggers when GoBackend integration is ready
      // await gobackendClient.workflow.trigger.mutate({
      //   entityType: 'opportunity',
      //   entityId: opportunity.id,
      //   trigger,
      //   data: opportunity
      // });
    } catch (error) {
      console.error('Error triggering automation workflows:', error);
    }
  }

  // 🛠️ Helper Methods
  
  private static shouldRecalculateScores(data: Partial<Opportunity>): boolean {
    const scoringFields = ['value', 'serviceType', 'buildingType', 'source', 'stage'];
    return scoringFields.some(field => field in data);
  }

  private static requiresSiteVisit(data: Partial<Opportunity>): boolean {
    return data.serviceType === 'installation' || 
           data.installationComplexity === 'complex' ||
           data.installationComplexity === 'extreme';
  }

  private static getDefaultProbabilityByStage(stage: OpportunityStage): number {
    const probabilities = {
      'NEW_LEAD': 10,
      'QUALIFIED': 25,
      'PROPOSAL': 50,
      'NEGOTIATION': 75,
      'IN_PROGRESS': 90,
      'CLOSED_WON': 100,
      'FOLLOW_UP': 100
    };
    return probabilities[stage] || 50;
  }

  private static getNextStage(currentStage: OpportunityStage): OpportunityStage | null {
    const stageOrder = ['NEW_LEAD', 'QUALIFIED', 'PROPOSAL', 'NEGOTIATION', 'IN_PROGRESS', 'CLOSED_WON', 'FOLLOW_UP'];
    const currentIndex = stageOrder.indexOf(currentStage);
    return currentIndex < stageOrder.length - 1 ? stageOrder[currentIndex + 1] as OpportunityStage : null;
  }

  private static getPreviousStage(currentStage: OpportunityStage): OpportunityStage | null {
    const stageOrder = ['NEW_LEAD', 'QUALIFIED', 'PROPOSAL', 'NEGOTIATION', 'IN_PROGRESS', 'CLOSED_WON', 'FOLLOW_UP'];
    const currentIndex = stageOrder.indexOf(currentStage);
    return currentIndex > 0 ? stageOrder[currentIndex - 1] as OpportunityStage : null;
  }

  private static getDaysInPipeline(opportunity: Opportunity): number {
    return Math.floor((Date.now() - opportunity.createdAt.getTime()) / (1000 * 60 * 60 * 24));
  }
}

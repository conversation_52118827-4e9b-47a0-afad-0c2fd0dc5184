# 🎨 Bento Grid - <PERSON><PERSON><PERSON><PERSON><PERSON>

## 📋 Spis Treści

1. [Podstawowe Layouts](#podstawowe-layouts)
2. [Dashboard Patterns](#dashboard-patterns)
3. [AI Integration](#ai-integration)
4. [Advanced Patterns](#advanced-patterns)
5. [Best Practices](#best-practices)

---

## 🏗️ Podstawowe Layouts {#podstawowe-layouts}

### 1. Simple Metrics Grid
```tsx
<BentoGrid>
  <BentoCard
    title="Dzisiejsze Wizyty"
    value={12}
    icon={<Calendar className="h-6 w-6" />}
  />
  <BentoCard
    title="Aktywni Klienci"
    value={1247}
    trend={5.2}
    icon={<Users className="h-6 w-6" />}
  />
  <BentoCard
    title="Przychód"
    value="125,000 zł"
    trend={12.5}
    icon={<DollarSign className="h-6 w-6" />}
  />
</BentoGrid>
```

### 2. Mixed Size Layout
```tsx
<BentoGrid>
  {/* Wide card for main metric */}
  <BentoCard
    title="Miesięczny Przegląd"
    size="wide"
    icon={<BarChart3 className="h-6 w-6" />}
  >
    <div className="grid grid-cols-2 gap-4 mt-4">
      <div>
        <div className="text-2xl font-bold">85%</div>
        <div className="text-sm text-gray-600">Cel miesięczny</div>
      </div>
      <div>
        <div className="text-2xl font-bold">1.2h</div>
        <div className="text-sm text-gray-600">Śr. czas reakcji</div>
      </div>
    </div>
  </BentoCard>

  {/* Normal cards */}
  <BentoCard title="Pilne" value={3} icon={<AlertTriangle />} />
  <BentoCard title="Ukończone" value={45} icon={<CheckCircle />} />

  {/* Tall card for detailed info */}
  <BentoCard
    title="Wykorzystanie Techników"
    value="87%"
    trend={3.1}
    size="tall"
  >
    <div className="mt-4 space-y-3">
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div className="bg-blue-600 h-2 rounded-full w-[87%]" />
      </div>
      <div className="text-xs text-gray-500">Cel: 90%</div>
    </div>
  </BentoCard>
</BentoGrid>
```

---

## 📊 Dashboard Patterns {#dashboard-patterns}

### 1. HVAC Service Dashboard
```tsx
const ServiceDashboard = ({ data }) => {
  return (
    <BentoGrid>
      {/* Today's Schedule - Wide */}
      <BentoCard
        title="Dzisiejszy Harmonogram"
        size="wide"
        icon={<Calendar />}
        onClick={() => navigate('/calendar')}
      >
        <div className="space-y-2 mt-4">
          {data.todaySchedule.map(item => (
            <div key={item.id} className="flex justify-between p-2 bg-gray-50 rounded">
              <span>{item.customer}</span>
              <span className="text-sm text-gray-600">{item.time}</span>
            </div>
          ))}
        </div>
      </BentoCard>

      {/* Quick Stats */}
      <BentoCard title="Oczekujące" value={data.pending} icon={<Clock />} />
      <BentoCard title="W Trakcie" value={data.inProgress} icon={<Wrench />} />

      {/* Performance - Tall */}
      <BentoCard
        title="Wydajność Zespołu"
        value={`${data.efficiency}%`}
        trend={data.efficiencyTrend}
        size="tall"
      >
        <div className="mt-4">
          <div className="text-sm text-gray-600 mb-2">Ostatnie 7 dni</div>
          <div className="space-y-2">
            {data.weeklyPerformance.map((day, index) => (
              <div key={index} className="flex items-center gap-2">
                <span className="text-xs w-8">{day.name}</span>
                <div className="flex-1 bg-gray-200 rounded-full h-1">
                  <div 
                    className="bg-blue-600 h-1 rounded-full"
                    style={{ width: `${day.value}%` }}
                  />
                </div>
                <span className="text-xs w-8">{day.value}%</span>
              </div>
            ))}
          </div>
        </div>
      </BentoCard>

      {/* Recent Activity - Large */}
      <BentoCard
        title="Ostatnia Aktywność"
        size="large"
        icon={<Activity />}
      >
        <div className="mt-4 space-y-3">
          {data.recentActivity.map(activity => (
            <div key={activity.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
              <div className={`w-2 h-2 rounded-full mt-2 ${
                activity.type === 'completed' ? 'bg-green-500' :
                activity.type === 'started' ? 'bg-blue-500' : 'bg-yellow-500'
              }`} />
              <div className="flex-1">
                <div className="font-medium text-sm">{activity.description}</div>
                <div className="text-xs text-gray-600">{activity.time}</div>
              </div>
            </div>
          ))}
        </div>
      </BentoCard>

      {/* Weather Impact */}
      <BentoCard
        title="Wpływ Pogody"
        value={`${data.temperature}°C`}
        icon={<Thermometer />}
      >
        <div className="mt-2 text-xs text-blue-600">
          Optymalne warunki pracy
        </div>
      </BentoCard>

      {/* Customer Satisfaction */}
      <BentoCard
        title="Zadowolenie Klientów"
        value={`${data.satisfaction}/5`}
        trend={data.satisfactionTrend}
        icon={<Star />}
      />
    </BentoGrid>
  );
};
```

### 2. Customer Analytics Dashboard
```tsx
const CustomerDashboard = ({ data }) => {
  return (
    <BentoGrid>
      {/* Total Customers */}
      <BentoCard
        title="Wszyscy Klienci"
        value={data.total}
        icon={<Users />}
      />

      {/* New This Month */}
      <BentoCard
        title="Nowi w tym miesiącu"
        value={data.newThisMonth}
        trend={data.growthRate}
        icon={<TrendingUp />}
      />

      {/* Top Cities - Large */}
      <BentoCard
        title="Najważniejsze Miasta"
        size="large"
        icon={<MapPin />}
      >
        <div className="mt-4 space-y-3">
          {data.topCities.map((city, index) => (
            <div key={city.name} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold ${
                  index === 0 ? 'bg-yellow-500' :
                  index === 1 ? 'bg-gray-400' : 'bg-orange-500'
                }`}>
                  {index + 1}
                </div>
                <span>{city.name}</span>
              </div>
              <span className="text-sm text-gray-600">{city.count}</span>
            </div>
          ))}
        </div>
      </BentoCard>

      {/* Priority Distribution - Wide */}
      <BentoCard
        title="Rozkład Priorytetów"
        size="wide"
        icon={<AlertTriangle />}
      >
        <div className="grid grid-cols-4 gap-4 mt-4">
          <div className="text-center">
            <div className="text-lg font-bold text-red-600">{data.urgent}</div>
            <div className="text-xs text-gray-600">Pilne</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-orange-600">{data.high}</div>
            <div className="text-xs text-gray-600">Wysokie</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-yellow-600">{data.medium}</div>
            <div className="text-xs text-gray-600">Średnie</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">{data.low}</div>
            <div className="text-xs text-gray-600">Niskie</div>
          </div>
        </div>
      </BentoCard>
    </BentoGrid>
  );
};
```

---

## 🤖 AI Integration {#ai-integration}

### 1. Smart Search with Suggestions
```tsx
const SmartSearchForm = () => {
  const [query, setQuery] = useState('');
  
  const handleSuggestionSelect = (suggestion) => {
    setQuery(suggestion.text);
    // Trigger search
    onSearch(suggestion.text);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Inteligentne Wyszukiwanie</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative">
          <Input
            placeholder="Opisz problem lub wpisz nazwę klienta..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
          <SmartSuggestions
            context={query}
            type="hvac_service"
            onSuggestionSelect={handleSuggestionSelect}
          />
        </div>
      </CardContent>
    </Card>
  );
};
```

### 2. AI-Enhanced Service Form
```tsx
const ServiceForm = () => {
  const [description, setDescription] = useState('');
  
  return (
    <BentoGrid>
      <BentoCard
        title="Opis Problemu"
        size="wide"
      >
        <div className="relative mt-4">
          <Textarea
            placeholder="Opisz problem z urządzeniem HVAC..."
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="min-h-[120px]"
          />
          <SmartSuggestions
            context={description}
            type="equipment_issue"
            onSuggestionSelect={(suggestion) => {
              setDescription(prev => prev + ' ' + suggestion.text);
            }}
          />
        </div>
      </BentoCard>

      <BentoCard
        title="AI Analiza"
        size="tall"
      >
        <div className="mt-4 space-y-3">
          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="font-medium text-sm">Prawdopodobna przyczyna</div>
            <div className="text-xs text-gray-600 mt-1">
              Problem z filtrem powietrza (85% pewności)
            </div>
          </div>
          <div className="p-3 bg-green-50 rounded-lg">
            <div className="font-medium text-sm">Zalecane działania</div>
            <div className="text-xs text-gray-600 mt-1">
              1. Sprawdź stan filtra<br/>
              2. Wymień jeśli potrzeba<br/>
              3. Sprawdź przepływ powietrza
            </div>
          </div>
        </div>
      </BentoCard>
    </BentoGrid>
  );
};
```

---

## 🎯 Advanced Patterns {#advanced-patterns}

### 1. Interactive Dashboard with State Management
```tsx
const InteractiveDashboard = () => {
  const [selectedMetric, setSelectedMetric] = useState(null);
  const [timeRange, setTimeRange] = useState('7d');

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Dashboard Analityczny</h1>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1d">Dziś</SelectItem>
            <SelectItem value="7d">7 dni</SelectItem>
            <SelectItem value="30d">30 dni</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <BentoGrid>
        {metrics.map(metric => (
          <BentoCard
            key={metric.id}
            title={metric.title}
            value={metric.value}
            trend={metric.trend}
            icon={metric.icon}
            className={selectedMetric === metric.id ? 'ring-2 ring-blue-500' : ''}
            onClick={() => setSelectedMetric(metric.id)}
          />
        ))}

        {/* Detailed view for selected metric */}
        {selectedMetric && (
          <BentoCard
            title="Szczegółowa Analiza"
            size="large"
          >
            <MetricDetailView metricId={selectedMetric} timeRange={timeRange} />
          </BentoCard>
        )}
      </BentoGrid>
    </div>
  );
};
```

### 2. Real-time Updates
```tsx
const RealTimeDashboard = () => {
  const [data, setData] = useState({});
  
  useEffect(() => {
    // WebSocket connection for real-time updates
    const ws = new WebSocket('ws://localhost:3001/dashboard');
    
    ws.onmessage = (event) => {
      const update = JSON.parse(event.data);
      setData(prev => ({ ...prev, ...update }));
    };

    return () => ws.close();
  }, []);

  return (
    <BentoGrid>
      <BentoCard
        title="Live Orders"
        value={data.liveOrders || 0}
        icon={<Activity className="h-6 w-6" />}
      >
        <div className="mt-2 flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-xs text-green-600">Na żywo</span>
        </div>
      </BentoCard>

      <BentoCard
        title="Active Technicians"
        value={data.activeTechnicians || 0}
        icon={<Users className="h-6 w-6" />}
      />
    </BentoGrid>
  );
};
```

---

## ✅ Best Practices {#best-practices}

### 1. Performance Optimization
```tsx
// Lazy load heavy components
const HeavyChart = lazy(() => import('./HeavyChart'));

// Memoize expensive calculations
const MetricCard = memo(({ data }) => {
  const processedData = useMemo(() => {
    return expensiveCalculation(data);
  }, [data]);

  return (
    <BentoCard title="Metric" value={processedData.value} />
  );
});

// Use Suspense for loading states
<Suspense fallback={<BentoCardSkeleton />}>
  <HeavyChart />
</Suspense>
```

### 2. Accessibility
```tsx
<BentoCard
  title="Accessible Card"
  value={123}
  // Add proper ARIA labels
  aria-label="Total orders: 123"
  // Keyboard navigation
  tabIndex={0}
  onKeyDown={(e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      onClick();
    }
  }}
  // Focus management
  className="focus:ring-2 focus:ring-blue-500 focus:outline-none"
/>
```

### 3. Error Handling
```tsx
const SafeBentoCard = ({ title, value, ...props }) => {
  if (!value && value !== 0) {
    return (
      <BentoCard
        title={title}
        value="--"
        icon={<AlertTriangle className="h-6 w-6 text-gray-400" />}
        className="opacity-50"
        {...props}
      >
        <div className="text-xs text-gray-500 mt-2">
          Dane niedostępne
        </div>
      </BentoCard>
    );
  }

  return <BentoCard title={title} value={value} {...props} />;
};
```

### 4. Responsive Design
```tsx
// Mobile-first approach
<BentoGrid className="grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
  <BentoCard
    title="Mobile Optimized"
    size="normal" // Automatically adjusts on mobile
    className="col-span-1 md:col-span-2" // Custom responsive behavior
  />
</BentoGrid>
```

---

**Tip**: Zawsze testuj komponenty na różnych urządzeniach i z różnymi danymi, aby zapewnić optymalne doświadczenie użytkownika!

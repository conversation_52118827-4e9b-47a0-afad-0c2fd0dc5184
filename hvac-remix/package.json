{"name": "hvac-remix", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "VITE_CJS_IGNORE_WARNING=true remix build && node patch-build.js", "dev": "dotenv -e .env -- remix dev", "dev:watch": "remix dev", "dev:serve": "remix-serve ./build/index.js", "format": "prettier --write .", "format:repo": "npm run format && npm run lint -- --fix", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "lint:fix": "eslint --cache --cache-location ./node_modules/.cache/eslint . --fix", "start": "remix-serve ./build/index.js", "start:mocks": "NODE_PATH=./app NODE_OPTIONS=\"--import ./mocks/index.js\" remix-serve ./build/index.js", "test": "vitest", "test:e2e:dev": "start-server-and-test dev http://localhost:3000 \"npx cypress open\"", "pretest:e2e:run": "npm run build", "test:e2e:run": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run\"", "test:e2e:customers": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/customers/**/*.cy.ts'\"", "test:e2e:devices": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/devices/**/*.cy.ts'\"", "test:e2e:service-orders": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/service-orders/**/*.cy.ts'\"", "test:e2e:calendar": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/calendar/**/*.cy.ts'\"", "test:e2e:search": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/search/**/*.cy.ts'\"", "test:e2e:ocr": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/ocr/**/*.cy.ts'\"", "test:e2e:predictive": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/predictive-maintenance/**/*.cy.ts'\"", "test:e2e:auth": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/auth/**/*.cy.ts'\"", "test:e2e:dashboard": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/dashboard/**/*.cy.ts'\"", "test:e2e:ai": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/ai/**/*.cy.ts'\"", "typecheck": "tsc && tsc -p cypress", "validate": "npm-run-all --parallel \"test -- --run\" lint typecheck test:e2e:run", "generate:og-images": "node scripts/generate-og-images.js", "analyze": "source-map-explorer 'build/client/**/*.js'", "analyze:bundle": "node scripts/analyze-bundle.js", "postbuild": "echo 'Skipping remix-pwa build for now'", "prod:build": "NODE_ENV=production npm run clean && NODE_ENV=production npm run build", "clean": "rimraf ./build ./public/build", "deploy:production": "bash scripts/deploy-production.sh", "security:check": "node scripts/security-check.js", "security:audit": "npm audit --production", "security:fix": "npm audit fix --production", "prod:optimize": "node scripts/production-optimize.js", "prod:analyze": "npm run prod:build && npm run analyze:bundle", "prod:test-load": "node scripts/load-testing.js", "prod:monitor-setup": "node scripts/setup-monitoring.js", "prod:readiness": "node scripts/production-readiness.js", "docs:generate": "node scripts/generate-docs.js", "docs:serve": "npx serve docs", "docs:build": "npm run docs:generate && npx markdown-to-html --input docs --output docs-html", "docs:deploy": "npm run docs:build && npx gh-pages -d docs-html"}, "eslintIgnore": ["/node_modules", "/build", "/public/build"], "dependencies": {"@apollo/client": "^3.13.8", "@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.8.2", "@copilotkit/react-core": "^1.8.13", "@copilotkit/react-ui": "^0.2.0", "@copilotkit/runtime": "^1.8.13", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@headlessui/react": "^1.7.18", "@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.1.1", "@prisma/client": "^6.8.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@reduxjs/toolkit": "^2.8.2", "@remix-pwa/cache": "^2.0.12", "@remix-pwa/sw": "^3.0.10", "@remix-pwa/sync": "^3.0.5", "@remix-pwa/worker-runtime": "^2.1.4", "@remix-run/node": "^2.16.7", "@remix-run/react": "^2.16.7", "@remix-run/serve": "^2.16.7", "@sentry/browser": "^7.120.3", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "@tanstack/react-query": "^5.77.2", "@trpc/client": "^11.1.2", "@trpc/react-query": "^11.1.2", "@trpc/server": "^11.1.3", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/ws": "^8.18.1", "@udecode/plate": "^48.0.5", "@udecode/plate-basic-elements": "^48.0.0", "@udecode/plate-basic-marks": "^48.0.0", "@udecode/plate-link": "^48.0.0", "@udecode/plate-list": "^48.0.0", "@udecode/plate-table": "^48.0.0", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.3.1", "dotenv": "^16.4.5", "esbuild": "^0.25.4", "estree-util-value-to-estree": "^3.4.0", "framer-motion": "^11.0.8", "gsap": "^3.12.5", "html2canvas": "^1.4.1", "isbot": "^5.1.1", "jspdf": "^3.0.1", "lucide-react": "^0.344.0", "nodemailer": "^7.0.3", "otplib": "^12.0.1", "pdfkit": "^0.17.1", "qrcode": "^1.5.3", "qrcode-generator": "^1.4.4", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "redis": "^5.1.1", "remix": "^2.16.8", "remix-pwa": "^4.0.5", "slate": "^0.114.0", "slate-dom": "^0.114.0", "slate-history": "^0.113.1", "slate-hyperscript": "^0.100.0", "slate-react": "^0.114.2", "speakeasy": "^2.0.0", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tiny-invariant": "^1.3.1", "uuid": "^9.0.1", "web-vitals": "^5.0.1", "workbox-background-sync": "^7.0.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^7.0.0", "workbox-expiration": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0", "ws": "^8.18.2", "xlsx": "^0.18.5"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@remix-pwa/dev": "^3.1.0", "@remix-run/dev": "^2.16.7", "@remix-run/eslint-config": "^2.16.7", "@tailwindcss/postcss": "^4.1.7", "@tanstack/react-query-devtools": "^5.77.2", "@testing-library/cypress": "^10.0.1", "@testing-library/jest-dom": "^6.6.3", "@types/bcryptjs": "^2.4.6", "@types/eslint": "^8.56.5", "@types/node": "^20.17.52", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.3.1", "autoprefixer": "^10.4.21", "cookie": "^1.0.2", "cross-env": "^7.0.3", "cypress": "^13.6.6", "dotenv-cli": "^8.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-cypress": "^2.15.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-jest-dom": "^5.1.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-markdown": "^3.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-testing-library": "^6.2.0", "happy-dom": "^17.4.7", "msw": "^2.2.2", "npm-run-all2": "^6.1.1", "postcss": "^8.5.4", "prettier": "3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "prisma": "^6.8.2", "rimraf": "^5.0.5", "source-map-explorer": "^2.5.3", "start-server-and-test": "^2.0.3", "tsx": "^4.7.1", "typescript": "^5.3.3", "vite": "^6.3.5", "vite-tsconfig-paths": "^4.3.1", "vitest": "^3.1.4"}, "engines": {"node": ">=18.0.0"}}
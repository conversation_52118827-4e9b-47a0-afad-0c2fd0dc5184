// Prosty skrypt do utworzenia przykładowych możliwości sprzedażowych
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createSampleData() {
  console.log('🌱 Tworzenie przykładowych danych dla pipeline sprzedaży...');

  try {
    // Najpierw sprawdź czy istnieją użytkownicy
    let adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      console.log('Tworzenie użytkownika admin...');
      adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Administrator',
          role: 'ADMIN'
        }
      });
    }

    // Sprawdź czy istnieją klienci
    let customers = await prisma.customer.findMany();
    
    if (customers.length === 0) {
      console.log('Tworzenie przykładowych klientów...');
      customers = await Promise.all([
        prisma.customer.create({
          data: {
            name: '<PERSON><PERSON><PERSON> "Księgowa"',
            email: '<EMAIL>',
            phone: '+48 22 123 45 67',
            address: 'ul. Marszałkowska 15/3',
            city: 'Warszawa',
            postalCode: '00-624',
            country: 'Polska',
            priority: 'HIGH',
            contractType: 'PREMIUM',
            userId: adminUser.id,
          },
        }),
        prisma.customer.create({
          data: {
            name: 'Restauracja "Smakosze"',
            email: '<EMAIL>',
            phone: '+48 22 987 65 43',
            address: 'ul. Nowy Świat 25',
            city: 'Warszawa',
            postalCode: '00-029',
            country: 'Polska',
            priority: 'MEDIUM',
            contractType: 'STANDARD',
            userId: adminUser.id,
          },
        }),
        prisma.customer.create({
          data: {
            name: 'Hotel "Warszawski"',
            email: '<EMAIL>',
            phone: '+48 22 555 77 88',
            address: 'ul. Krakowskie Przedmieście 10',
            city: 'Warszawa',
            postalCode: '00-068',
            country: 'Polska',
            priority: 'HIGH',
            contractType: 'PREMIUM',
            userId: adminUser.id,
          },
        })
      ]);
    }

    // Sprawdź czy istnieją możliwości sprzedażowe
    const existingOpportunities = await prisma.opportunity.findMany();
    
    if (existingOpportunities.length === 0) {
      console.log('Tworzenie przykładowych możliwości sprzedażowych...');
      
      const opportunities = await Promise.all([
        // NEW_LEAD
        prisma.opportunity.create({
          data: {
            name: 'Instalacja klimatyzacji w nowym biurze',
            description: 'Klient planuje rozbudowę biura o 3 dodatkowe pomieszczenia',
            stage: 'NEW_LEAD',
            probability: 10,
            value: 15000.00,
            expectedCloseDate: new Date('2025-02-28'),
            serviceType: 'INSTALLATION',
            equipmentType: 'Multi-split system',
            installationComplexity: 'MEDIUM',
            roomCount: 3,
            totalArea: 120.0,
            buildingType: 'COMMERCIAL',
            ownerId: adminUser.id,
            leadSource: 'REFERRAL',
            customerId: customers[0].id,
          },
        }),
        
        // QUALIFIED
        prisma.opportunity.create({
          data: {
            name: 'Modernizacja systemu HVAC restauracji',
            description: 'Wymiana starego systemu na energooszczędny',
            stage: 'QUALIFIED',
            probability: 25,
            value: 25000.00,
            expectedCloseDate: new Date('2025-03-15'),
            serviceType: 'INSTALLATION',
            equipmentType: 'VRF System',
            installationComplexity: 'COMPLEX',
            roomCount: 5,
            totalArea: 200.0,
            buildingType: 'COMMERCIAL',
            ownerId: adminUser.id,
            leadSource: 'WEBSITE',
            customerId: customers[1].id,
          },
        }),
        
        // PROPOSAL
        prisma.opportunity.create({
          data: {
            name: 'System klimatyzacji dla hotelu',
            description: 'Kompleksowy system HVAC dla 50 pokoi hotelowych',
            stage: 'PROPOSAL',
            probability: 50,
            value: 85000.00,
            expectedCloseDate: new Date('2025-04-30'),
            serviceType: 'INSTALLATION',
            equipmentType: 'Central HVAC System',
            installationComplexity: 'COMPLEX',
            roomCount: 50,
            totalArea: 1500.0,
            buildingType: 'COMMERCIAL',
            ownerId: adminUser.id,
            leadSource: 'REFERRAL',
            customerId: customers[2].id,
          },
        }),
        
        // NEGOTIATION
        prisma.opportunity.create({
          data: {
            name: 'Serwis klimatyzacji biurowej',
            description: 'Roczny kontrakt serwisowy dla 10 jednostek',
            stage: 'NEGOTIATION',
            probability: 75,
            value: 12000.00,
            expectedCloseDate: new Date('2025-02-15'),
            serviceType: 'MAINTENANCE',
            equipmentType: 'Split AC Units',
            installationComplexity: 'SIMPLE',
            roomCount: 10,
            totalArea: 300.0,
            buildingType: 'COMMERCIAL',
            ownerId: adminUser.id,
            leadSource: 'EXISTING_CUSTOMER',
            customerId: customers[0].id,
          },
        }),
        
        // IN_PROGRESS
        prisma.opportunity.create({
          data: {
            name: 'Naprawa systemu chłodzenia',
            description: 'Pilna naprawa systemu chłodzenia w kuchni restauracji',
            stage: 'IN_PROGRESS',
            probability: 90,
            value: 5500.00,
            expectedCloseDate: new Date('2025-01-20'),
            serviceType: 'REPAIR',
            equipmentType: 'Commercial Refrigeration',
            installationComplexity: 'MEDIUM',
            roomCount: 1,
            totalArea: 50.0,
            buildingType: 'COMMERCIAL',
            ownerId: adminUser.id,
            leadSource: 'EMERGENCY_CALL',
            customerId: customers[1].id,
          },
        }),
        
        // CLOSED_WON
        prisma.opportunity.create({
          data: {
            name: 'Instalacja klimatyzacji w recepcji',
            description: 'Zakończona instalacja systemu klimatyzacji w recepcji hotelu',
            stage: 'CLOSED_WON',
            probability: 100,
            value: 8500.00,
            expectedCloseDate: new Date('2025-01-10'),
            actualCloseDate: new Date('2025-01-08'),
            serviceType: 'INSTALLATION',
            equipmentType: 'Split AC Unit',
            installationComplexity: 'SIMPLE',
            roomCount: 1,
            totalArea: 80.0,
            buildingType: 'COMMERCIAL',
            ownerId: adminUser.id,
            leadSource: 'REFERRAL',
            customerId: customers[2].id,
          },
        }),
        
        // FOLLOW_UP
        prisma.opportunity.create({
          data: {
            name: 'Przegląd gwarancyjny systemu HVAC',
            description: 'Przegląd gwarancyjny po roku od instalacji',
            stage: 'FOLLOW_UP',
            probability: 100,
            value: 2500.00,
            expectedCloseDate: new Date('2025-01-15'),
            actualCloseDate: new Date('2025-01-12'),
            serviceType: 'MAINTENANCE',
            equipmentType: 'Multi-split system',
            installationComplexity: 'SIMPLE',
            roomCount: 3,
            totalArea: 120.0,
            buildingType: 'COMMERCIAL',
            ownerId: adminUser.id,
            leadSource: 'EXISTING_CUSTOMER',
            customerId: customers[0].id,
          },
        })
      ]);

      console.log(`✅ Utworzono ${opportunities.length} przykładowych możliwości sprzedażowych!`);
    } else {
      console.log(`ℹ️  Znaleziono ${existingOpportunities.length} istniejących możliwości sprzedażowych.`);
    }

    console.log('🎉 Przykładowe dane zostały utworzone pomyślnie!');
    console.log('🔗 Możesz teraz przetestować pipeline na: http://localhost:3000/opportunities/pipeline');

  } catch (error) {
    console.error('❌ Błąd podczas tworzenia przykładowych danych:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createSampleData();
